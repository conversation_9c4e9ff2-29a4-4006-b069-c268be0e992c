2025-08-01 11:01:41 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 11:01:41 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 11:01:41 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-01 11:01:41 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 11:01:41 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:01:41 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-01 11:01:41 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 11:01:41 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 11:01:41 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 11:01:41 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 11:01:42 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 11:01:42 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 11:01:45 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 11:01:47 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 11:01:47 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-08-01 11:01:47 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 11:01:47 线程1：[信息] 浏览器启动成功
2025-08-01 11:01:47 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-01 11:01:47 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-01 11:01:47 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 11:01:47 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 11:01:47 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 11:01:47 线程1：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 11:01:47 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-01 11:01:47 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 11:01:47 线程1：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 11:01:47 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 11:01:47 线程1：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 11:01:47 线程1：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 11:01:47 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 11:01:47 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-08-01 11:01:47 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 11:01:47 线程2：[信息] 浏览器启动成功
2025-08-01 11:01:47 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-01 11:01:48 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-01 11:01:48 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 11:01:48 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 11:01:48 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 11:01:48 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 11:01:48 线程2：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 11:01:48 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-01 11:01:48 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 11:01:48 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 11:01:48 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 11:01:48 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 11:01:48 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-01 11:01:48 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 11:01:48 线程2：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 11:01:48 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 11:01:48 线程2：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-01 11:01:48 线程2：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 11:01:48 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 11:01:49 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 11:01:49 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-01 11:01:49 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 11:01:49 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 11:01:49 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-01 11:01:49 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 11:02:03 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-01 11:02:03 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 11:02:03 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 11:02:03 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 11:02:03 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 11:02:04 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 11:02:07 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 11:02:07 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 11:02:07 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 11:02:07 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 11:02:08 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:02:08 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 11:02:08 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 11:02:08 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 11:02:08 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 11:02:08 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:02:08 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-01 11:02:08 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 11:02:08 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 11:02:08 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 11:02:08 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 11:02:09 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 11:02:12 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 34805 字节 (进度: 100%)
2025-08-01 11:02:12 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，34805字节，复杂度符合要求 (进度: 100%)
2025-08-01 11:02:12 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 11:02:12 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 11:02:12 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 11:02:12 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 11:02:12 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 11:02:12 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:02:12 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 11:02:12 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 11:02:12 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 11:02:12 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 11:02:12 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:02:14 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"5b64bf"},"taskId":"ec44faa4-6e83-11f0-a8fa-5a3e33407522"} (进度: 100%)
2025-08-01 11:02:14 线程1：[信息] [信息] 第一页第1次识别结果: 5b64bf → 转换为小写: 5b64bf (进度: 100%)
2025-08-01 11:02:14 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 11:02:14 线程1：[信息] [信息] 已填入验证码: 5b64bf (进度: 100%)
2025-08-01 11:02:14 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 11:02:15 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 34875 字节 (进度: 100%)
2025-08-01 11:02:15 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，34875字节，复杂度符合要求 (进度: 100%)
2025-08-01 11:02:15 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 11:02:17 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 11:02:17 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 11:02:17 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 11:02:17 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 11:02:17 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 11:02:17 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 11:02:17 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 11:02:17 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 11:02:17 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 11:02:17 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 11:02:17 [信息] [线程1] 已删除旧的响应文件
2025-08-01 11:02:17 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-01 11:02:18 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"s8nn4z"},"taskId":"eedc4812-6e83-11f0-b641-b26e8748d9fc"} (进度: 100%)
2025-08-01 11:02:18 线程2：[信息] [信息] 第一页第1次识别结果: s8nn4z → 转换为小写: s8nn4z (进度: 100%)
2025-08-01 11:02:18 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 11:02:18 线程2：[信息] [信息] 已填入验证码: s8nn4z (进度: 100%)
2025-08-01 11:02:18 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 11:02:19 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 11:02:19 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:02:19
2025-08-01 11:02:20 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 11:02:20 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 11:02:20 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 11:02:20 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 11:02:20 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 11:02:20 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 11:02:20 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 11:02:20 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 11:02:20 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-01 11:02:20 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 11:02:20 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 11:02:22 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 11:02:22 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:02:22
2025-08-01 11:02:22 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 11:02:22 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 11:02:22
2025-08-01 11:02:25 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 11:02:25 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:02:25
2025-08-01 11:02:25 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 11:02:25 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 11:02:25
2025-08-01 11:02:26 [信息] [线程1] 邮箱验证码获取成功: 102850，立即停止重复请求
2025-08-01 11:02:26 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-01 11:02:26 [信息] [线程1] 已清理响应文件
2025-08-01 11:02:26 线程1：[信息] [信息] 验证码获取成功: 102850，正在自动填入... (进度: 25%)
2025-08-01 11:02:26 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-01 11:02:26 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-01 11:02:26 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-01 11:02:26 [信息] 线程1完成第二页事件已处理
2025-08-01 11:02:26 [信息] 线程1完成第二页，开始批量获取手机号码...
2025-08-01 11:02:26 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-01 11:02:26 [信息] 开始批量获取2个手机号码，服务商: USA
2025-08-01 11:02:26 [信息] 美国API开始批量获取2个手机号码
2025-08-01 11:02:26 [信息] [美国API] 开始批量购买2个手机号码
2025-08-01 11:02:26 [信息] [美国API] 请求购买2个手机号码: https://www.api21k.com/api/v1/buy/create
2025-08-01 11:02:27 [信息] [美国API] 购买响应: {"code":1,"msg":"购买成功","time":1754017346,"data":{"ordernum":"25080111022700256","api_count":2}}
2025-08-01 11:02:27 [信息] [美国API] 购买成功，订单号: 25080111022700256，数量: 2
2025-08-01 11:02:27 [信息] [美国API] 批量购买成功，订单号: 25080111022700256
2025-08-01 11:02:27 [信息] [美国API] 请求获取手机号码列表: https://www.api21k.com/api/v1/order/list
2025-08-01 11:02:28 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 11:02:28 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 11:02:28
2025-08-01 11:02:29 [信息] [美国API] 获取列表响应: {"code":1,"msg":"获取订单列表成功","time":1754017347,"data":{"list":[{"id":736583,"ordernum":"25080111022700256","app_id":81,"cate_id":2,"type":1,"num":2,"remark":"","status":0,"create_time":"2025-08-01 11:02:27","smsApp":{"name":"亚马逊（新运营商）"}}],"total":1}}
2025-08-01 11:02:29 [错误] [美国API] 获取列表失败: {"code":1,"msg":"获取订单列表成功","time":1754017347,"data":{"list":[{"id":736583,"ordernum":"25080111022700256","app_id":81,"cate_id":2,"type":1,"num":2,"remark":"","status":0,"create_time":"2025-08-01 11:02:27","smsApp":{"name":"亚马逊（新运营商）"}}],"total":1}}
2025-08-01 11:02:29 [错误] 美国API批量获取手机号码失败: {"code":1,"msg":"获取订单列表成功","time":1754017347,"data":{"list":[{"id":736583,"ordernum":"25080111022700256","app_id":81,"cate_id":2,"type":1,"num":2,"remark":"","status":0,"create_time":"2025-08-01 11:02:27","smsApp":{"name":"亚马逊（新运营商）"}}],"total":1}}
2025-08-01 11:02:29 [信息] 批量获取失败，各线程将使用手动模式
2025-08-01 11:02:29 [警告] 批量获取手机号码失败，各线程将使用单独获取模式
2025-08-01 11:02:29 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-01 11:02:29 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-01 11:02:29 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 11:02:29 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 11:02:29 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-01 11:02:30 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-01 11:02:31 [信息] [线程2] 第4次触发邮箱验证码获取...（最多20次）
2025-08-01 11:02:31 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 11:02:31
2025-08-01 11:02:33 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-01 11:02:33 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-01 11:02:33 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-01 11:02:34 [信息] [线程2] 第5次触发邮箱验证码获取...（最多20次）
2025-08-01 11:02:34 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 11:02:34
2025-08-01 11:02:35 [信息] [线程2] 邮箱验证码获取成功: 155634，立即停止重复请求
2025-08-01 11:02:35 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-01 11:02:35 [信息] [线程2] 已清理响应文件
2025-08-01 11:02:35 线程2：[信息] [信息] 验证码获取成功: 155634，正在自动填入... (进度: 25%)
2025-08-01 11:02:35 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-01 11:02:35 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-01 11:02:36 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-01 11:02:36 [信息] 线程2完成第二页事件已处理
2025-08-01 11:02:36 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-01 11:02:36 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-01 11:02:37 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-01 11:02:37 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-01 11:02:39 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-01 11:02:39 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-01 11:02:39 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 11:02:39 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 11:02:39 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-01 11:02:40 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-01 11:02:40 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-01 11:02:41 线程1：[信息] [信息] 数据国家代码为AZ，需要选择Azerbaijan (进度: 38%)
2025-08-01 11:02:42 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-01 11:02:43 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-01 11:02:43 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-01 11:02:43 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-01 11:02:44 线程1：[信息] [信息] 已选择国家: Azerbaijan (进度: 38%)
2025-08-01 11:02:44 线程1：[信息] [信息] 已成功选择国家: Azerbaijan (进度: 38%)
2025-08-01 11:02:44 线程1：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 38%)
2025-08-01 11:02:44 线程1：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 38%)
2025-08-01 11:02:44 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 11:02:45 线程1：[信息] [信息] 已选择国家代码 +1 (进度: 38%)
2025-08-01 11:02:46 线程1：[信息] [信息] 开始检查手机号码状态... (进度: 38%)
2025-08-01 11:02:46 线程1：[信息] [信息]  检查页面是否已有手机号码填写... (进度: 38%)
2025-08-01 11:02:46 线程1：[信息] [信息]  页面手机号码输入框当前值: '' (进度: 38%)
2025-08-01 11:02:46 线程1：[信息] [信息]  页面手机号码输入框为空，需要手动输入 (进度: 38%)
2025-08-01 11:02:46 线程1：[信息] [信息]  最终决定：显示手动输入弹窗 (进度: 38%)
2025-08-01 11:02:46 线程1：[信息] [信息] 🔴 请手动输入手机号码，然后点击【继续注册】 (进度: 38%)
2025-08-01 11:02:47 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-01 11:02:47 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-01 11:03:03 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-01 11:03:05 线程2：[信息] [信息] 数据国家代码为AZ，需要选择Azerbaijan (进度: 38%)
2025-08-01 11:03:06 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-01 11:03:08 线程2：[信息] [信息] 已选择国家: Azerbaijan (进度: 38%)
2025-08-01 11:03:08 线程2：[信息] [信息] 已成功选择国家: Azerbaijan (进度: 38%)
2025-08-01 11:03:08 线程2：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 38%)
2025-08-01 11:03:08 线程2：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 38%)
2025-08-01 11:03:08 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 11:03:09 线程2：[信息] [信息] 已选择国家代码 +1 (进度: 38%)
2025-08-01 11:03:10 线程2：[信息] [信息] 开始检查手机号码状态... (进度: 38%)
2025-08-01 11:03:10 线程2：[信息] [信息]  检查页面是否已有手机号码填写... (进度: 38%)
2025-08-01 11:03:10 线程2：[信息] [信息]  页面手机号码输入框当前值: '' (进度: 38%)
2025-08-01 11:03:10 线程2：[信息] [信息]  页面手机号码输入框为空，需要手动输入 (进度: 38%)
2025-08-01 11:03:10 线程2：[信息] [信息]  最终决定：显示手动输入弹窗 (进度: 38%)
2025-08-01 11:03:10 线程2：[信息] [信息] 🔴 请手动输入手机号码，然后点击【继续注册】 (进度: 38%)
2025-08-01 11:03:43 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForPhoneInput，当前步骤: 4 (进度: 38%)
2025-08-01 11:03:43 线程1：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-08-01 11:03:43 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-08-01 11:03:43 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-08-01 11:03:45 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 38%)
2025-08-01 11:03:45 线程1：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-01 11:03:45 线程1：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-01 11:03:45 线程1：[信息] [信息] 正在从页面读取手机号码... (进度: 100%)
2025-08-01 11:03:46 线程1：[信息] [信息] 从页面读取并保存手机号码: 16198124171（不含区号） (进度: 100%)
2025-08-01 11:03:46 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-01 11:03:49 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForPhoneInput，当前步骤: 4 (进度: 38%)
2025-08-01 11:03:49 线程2：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-08-01 11:03:49 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-08-01 11:03:49 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-08-01 11:03:49 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 38%)
2025-08-01 11:03:49 线程2：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-01 11:03:49 线程2：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-01 11:03:49 线程2：[信息] [信息] 正在从页面读取手机号码... (进度: 100%)
2025-08-01 11:03:49 线程2：[信息] [信息] 从页面读取并保存手机号码: 14794122536（不含区号） (进度: 100%)
2025-08-01 11:03:49 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-01 11:03:49 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-01 11:03:50 线程1：[信息] [信息] 正在选择月份: October (进度: 100%)
2025-08-01 11:03:51 线程1：[信息] [信息] 已选择月份（标准选项）: October (进度: 100%)
2025-08-01 11:03:51 线程1：[信息] [信息] 正在选择年份: 2025 (进度: 100%)
2025-08-01 11:03:52 线程1：[信息] [信息] 已选择年份（标准选项）: 2025 (进度: 100%)
2025-08-01 11:03:52 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-01 11:03:52 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-01 11:03:52 线程1：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 100%)
2025-08-01 11:03:52 线程1：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 100%)
2025-08-01 11:03:53 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-01 11:03:53 线程2：[信息] [信息] 正在选择月份: October (进度: 100%)
2025-08-01 11:03:54 线程2：[信息] [信息] 已选择月份（标准选项）: October (进度: 100%)
2025-08-01 11:03:54 线程2：[信息] [信息] 正在选择年份: 2025 (进度: 100%)
2025-08-01 11:03:55 线程2：[信息] [信息] 已选择年份（标准选项）: 2025 (进度: 100%)
2025-08-01 11:03:55 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-01 11:03:55 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-01 11:03:55 线程2：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 100%)
2025-08-01 11:03:55 线程2：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 100%)
2025-08-01 11:03:57 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-01 11:03:58 线程1：[信息] [信息] 已选择国家代码: +1 (进度: 100%)
2025-08-01 11:03:58 线程1：[信息] [信息] 已清空并重新填写手机号码: 16198124171 (进度: 100%)
2025-08-01 11:03:58 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-01 11:04:00 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-01 11:04:00 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-01 11:04:00 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-01 11:04:00 线程1：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-01 11:04:00 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-01 11:04:01 线程2：[信息] [信息] 已选择国家代码: +1 (进度: 100%)
2025-08-01 11:04:01 线程2：[信息] [信息] 已清空并重新填写手机号码: 14794122536 (进度: 100%)
2025-08-01 11:04:02 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-01 11:04:03 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-01 11:04:03 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:04:04 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-01 11:04:04 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-01 11:04:04 线程2：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-01 11:04:04 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-01 11:04:07 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-01 11:04:07 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:04:08 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35630 字节 (进度: 100%)
2025-08-01 11:04:08 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35630字节，复杂度符合要求 (进度: 100%)
2025-08-01 11:04:08 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 11:04:09 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"s5fxr3"},"taskId":"30f52fa2-6e84-11f0-b5bc-5a3e33407522"} (进度: 100%)
2025-08-01 11:04:09 线程1：[信息] [信息] 第六页第1次识别结果: s5fxr3 → 转换为小写: s5fxr3 (进度: 100%)
2025-08-01 11:04:09 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 11:04:09 线程1：[信息] [信息] 第六页已填入验证码: s5fxr3 (进度: 100%)
2025-08-01 11:04:09 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 11:04:10 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34739 字节 (进度: 100%)
2025-08-01 11:04:10 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，34739字节，复杂度符合要求 (进度: 100%)
2025-08-01 11:04:10 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 11:04:11 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"ssn78p"},"taskId":"328f05b8-6e84-11f0-baef-b26e8748d9fc"} (进度: 100%)
2025-08-01 11:04:11 线程2：[信息] [信息] 第六页第1次识别结果: ssn78p → 转换为小写: ssn78p (进度: 100%)
2025-08-01 11:04:11 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 11:04:12 线程2：[信息] [信息] 第六页已填入验证码: ssn78p (进度: 100%)
2025-08-01 11:04:12 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 11:04:12 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-01 11:04:12 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-01 11:04:16 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-01 11:04:16 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-01 11:04:16 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-01 11:04:19 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-01 11:04:19 线程1：[信息] [信息] 第七页手动模式：请手动输入验证码 (进度: 100%)
2025-08-01 11:04:19 线程1：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-01 11:04:19 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-01 11:04:19 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-01 11:04:19 线程1：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-01 11:04:20 线程1：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 7013位 (进度: 100%)
2025-08-01 11:04:20 线程1：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-01 11:04:20 线程1：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-01 11:04:20 线程1：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-01 11:04:22 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-01 11:04:22 线程2：[信息] [信息] 第七页手动模式：请手动输入验证码 (进度: 100%)
2025-08-01 11:04:22 线程2：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-01 11:04:22 线程2：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-01 11:04:24 线程1：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-01 11:04:24 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-01 11:04:24 线程2：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-01 11:04:24 线程2：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 6969位 (进度: 100%)
2025-08-01 11:04:24 线程2：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-01 11:04:24 线程2：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-01 11:04:25 线程2：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-01 11:04:28 线程2：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-01 11:04:28 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-01 11:04:35 线程2：[信息] [信息] 点击完成注册按钮失败: Target page, context or browser has been closed
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 100%)
2025-08-01 11:04:35 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-01 11:04:36 线程1：[信息] [信息] 点击完成注册按钮失败: Target page, context or browser has been closed
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 100%)
2025-08-01 11:04:36 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-01 11:04:38 [信息] 获取线程1当前数据: <EMAIL>
2025-08-01 11:04:38 线程1：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-01 11:04:38 线程1：[信息] 数据详情: <EMAIL>|kis68EoG|Sevil Ahmadov|SapphireTrail Ventures|6263 Heydar Aliyev Avenue|Ganja|GA|AZ2002|5235222235657000|10|25|200|Sevil Ahmadov|8TDMww21zo7|AZ
2025-08-01 11:04:38 线程1：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-01 11:04:38 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：8TDMww21zo7 ③AWS密码：kis68EoG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-01 11:04:38 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：8TDMww21zo7 ③AWS密码：kis68EoG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:38 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：8TDMww21zo7 ③AWS密码：kis68EoG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:38 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：8TDMww21zo7 ③AWS密码：kis68EoG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:38 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：8TDMww21zo7 ③AWS密码：kis68EoG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:38 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：8TDMww21zo7 ③AWS密码：kis68EoG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:38 线程1：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-01 11:04:38 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-01 11:04:38 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：8TDMww21zo7 ③AWS密码：kis68EoG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-01 11:04:38 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：8TDMww21zo7 ③AWS密码：kis68EoG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:38 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：8TDMww21zo7 ③AWS密码：kis68EoG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:38 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：8TDMww21zo7 ③AWS密码：kis68EoG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:38 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：8TDMww21zo7 ③AWS密码：kis68EoG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:38 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：8TDMww21zo7 ③AWS密码：kis68EoG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:38 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-01 11:04:38 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250801_110139
2025-08-01 11:04:38 线程1：[信息] 已终止
2025-08-01 11:04:38 [信息] 线程1已终止
2025-08-01 11:04:38 [信息] 开始处理线程1终止数据，共1个数据
2025-08-01 11:04:38 [信息] 处理线程1终止数据: <EMAIL>
2025-08-01 11:04:38 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-01 11:04:38 [信息] 线程1终止 - 数据已移动到终止列表: <EMAIL>
2025-08-01 11:04:38 [信息] 线程1终止数据处理完成，成功移动1个数据
2025-08-01 11:04:38 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:04:38 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 11:04:38 [信息] 线程1已终止
2025-08-01 11:04:39 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-01 11:04:39 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-01 11:04:39 线程2：[信息] [信息] ❌ 控制台按钮处理失败: Target page, context or browser has been closed (进度: 100%)
2025-08-01 11:04:39 [信息] 控制台按钮处理失败: Target page, context or browser has been closed
2025-08-01 11:04:39 线程2：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-01 11:04:39 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-01 11:04:39 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：5D59N3R249NK ③AWS密码：zqMr1QVp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-01 11:04:39 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：5D59N3R249NK ③AWS密码：zqMr1QVp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 11:04:39 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：5D59N3R249NK ③AWS密码：zqMr1QVp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 11:04:39 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：5D59N3R249NK ③AWS密码：zqMr1QVp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 11:04:39 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：5D59N3R249NK ③AWS密码：zqMr1QVp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 11:04:39 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：5D59N3R249NK ③AWS密码：zqMr1QVp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 11:04:39 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-01 11:04:39 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:04:39 [信息] 多线程状态已重置
2025-08-01 11:04:39 线程2：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-01 11:04:39 线程2：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-01 11:04:39 [信息] 注册完成（密钥提取失败）
2025-08-01 11:04:39 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-01 11:04:39 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-01 11:04:39 [信息] 已完成数据移除: <EMAIL>
2025-08-01 11:04:39 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-01 11:04:39 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:04:39 [信息] 多线程状态已重置
2025-08-01 11:04:39 线程2：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-01 11:04:39 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:04:39 [信息] 多线程状态已重置
2025-08-01 11:04:39 [信息] 线程2数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-01 11:04:39 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:04:39 [信息] 多线程状态已重置
2025-08-01 11:04:39 线程2：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-01 11:04:39 线程2：[信息] 已继续
2025-08-01 11:04:39 [信息] 线程2已继续
2025-08-01 11:04:39 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 88%)
2025-08-01 11:04:39 线程1：[信息] [信息] 注册已被终止，停止密钥提取流程 (进度: 98%)
2025-08-01 11:04:39 线程1：[信息] [信息] 注册已被终止 (进度: 98%)
2025-08-01 11:04:39 线程1：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 98%)
2025-08-01 11:04:39 线程1：[信息] 已继续
2025-08-01 11:04:39 [信息] 线程1已继续
2025-08-01 11:04:41 [信息] 获取线程2当前数据: 无
2025-08-01 11:04:41 线程2：[信息] 终止时没有正在处理的数据
2025-08-01 11:04:41 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:04:41 [信息] 多线程状态已重置
2025-08-01 11:04:41 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:04:41 [信息] 多线程状态已重置
2025-08-01 11:04:41 线程2：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-01 11:04:41 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:04:41 [信息] 多线程状态已重置
2025-08-01 11:04:41 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-01 11:04:41 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:04:41 [信息] 多线程状态已重置
2025-08-01 11:04:41 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：5D59N3R249NK ③AWS密码：zqMr1QVp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-01 11:04:41 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：5D59N3R249NK ③AWS密码：zqMr1QVp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:41 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：5D59N3R249NK ③AWS密码：zqMr1QVp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:41 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：5D59N3R249NK ③AWS密码：zqMr1QVp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:41 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：5D59N3R249NK ③AWS密码：zqMr1QVp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:41 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：5D59N3R249NK ③AWS密码：zqMr1QVp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:04:41 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:04:41 [信息] 多线程状态已重置
2025-08-01 11:04:41 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-01 11:04:41 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250801_110139
2025-08-01 11:04:41 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:04:41 [信息] 多线程状态已重置
2025-08-01 11:04:41 线程2：[信息] 已终止
2025-08-01 11:04:41 [信息] 线程2已终止
2025-08-01 11:04:41 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:04:41 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-01 11:04:41 [信息] 线程2已终止
2025-08-01 11:04:42 [信息] 多线程窗口引用已清理
2025-08-01 11:04:42 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-01 11:04:42 [信息] 多线程管理窗口正在关闭
