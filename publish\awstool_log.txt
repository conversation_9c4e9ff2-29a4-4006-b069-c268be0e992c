2025-08-01 13:25:47 [信息] UniformGrid列数已更新为: 1
2025-08-01 13:25:47 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 13:25:47 线程3：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 13:25:47 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 13:25:47 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-01 13:25:47 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 13:25:48 [信息] UniformGrid列数已更新为: 1
2025-08-01 13:25:48 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-01 13:25:48 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 13:25:48 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 13:25:48 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-01 13:25:48 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 13:25:48 [信息] UniformGrid列数已更新为: 2
2025-08-01 13:25:48 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-01 13:25:48 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 13:25:48 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 13:25:48 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 13:25:48 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 13:25:50 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 13:25:50 线程3：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 13:25:50 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 13:25:54 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 13:25:54 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 13:25:54 线程3：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 13:25:55 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-08-01 13:25:55 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 13:25:55 线程2：[信息] 浏览器启动成功
2025-08-01 13:25:55 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-01 13:25:55 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-01 13:25:55 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 0%)
2025-08-01 13:25:55 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 13:25:55 线程3：[信息] 浏览器启动成功
2025-08-01 13:25:55 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 13:25:55 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-01 13:25:55 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 13:25:55 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 13:25:55 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-01 13:25:55 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-01 13:25:55 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 13:25:55 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 13:25:55 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 13:25:55 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-08-01 13:25:55 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 13:25:55 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 13:25:55 线程1：[信息] 浏览器启动成功
2025-08-01 13:25:55 线程3：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-01 13:25:55 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-01 13:25:55 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 13:25:56 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-01 13:25:56 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 13:25:56 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 13:25:56 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 13:25:56 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-01 13:25:56 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 13:25:56 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-01 13:25:56 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-01 13:25:56 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-01 13:25:56 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 13:25:56 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-01 13:25:56 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 13:25:56 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 13:25:56 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 13:25:56 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 13:25:56 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 13:25:56 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-01 13:25:56 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 13:25:57 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-01 13:25:57 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-01 13:25:57 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 13:25:57 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 13:25:57 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 13:25:57 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 13:26:13 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-01 13:26:13 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 13:26:13 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 13:26:13 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 13:26:13 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 13:26:13 线程3：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程3 - AWS注册 (进度: 98%)
2025-08-01 13:26:13 线程3：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 13:26:13 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 13:26:15 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 13:26:15 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 13:26:15 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-01 13:26:15 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 13:26:15 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 13:26:15 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 13:26:15 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 13:26:15 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 13:26:16 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 13:26:16 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 13:26:18 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 13:26:18 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 13:26:19 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 13:26:19 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 13:26:19 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 13:26:19 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-01 13:26:19 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 13:26:19 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 13:26:19 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 13:26:19 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 13:26:19 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 13:26:19 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 13:26:19 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 13:26:19 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 13:26:19 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 13:26:19 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-01 13:26:19 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 13:26:19 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-01 13:26:21 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-01 13:26:21 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-01 13:26:21 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-01 13:26:21 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 13:26:21 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-01 13:26:21 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-01 13:26:21 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 13:26:21 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 13:26:21 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 13:26:21 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-01 13:26:21 线程2：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-01 13:26:21 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 13:26:21 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 13:26:21 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 13:26:21 线程3：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-01 13:26:21 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 13:26:21 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 13:26:21 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 13:26:21 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 13:26:21 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 13:26:21 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 13:26:21 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 13:26:21 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 13:26:21 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 13:26:21 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 13:26:21 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-01 13:26:21 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 13:26:21 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 13:26:21 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 13:26:21 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 13:26:21 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-01 13:26:21 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 13:26:21 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 13:26:23 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 13:26:23 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 13:26:23
2025-08-01 13:26:23 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 13:26:23 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 13:26:23
2025-08-01 13:26:26 线程3：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 34890 字节 (进度: 100%)
2025-08-01 13:26:26 线程3：[信息] [信息] ✅ 图片验证通过：201x71px，34890字节，复杂度符合要求 (进度: 100%)
2025-08-01 13:26:26 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 13:26:26 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 13:26:26 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 13:26:26
2025-08-01 13:26:26 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 13:26:26 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 13:26:26
2025-08-01 13:26:29 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"3n2884"},"taskId":"1322c93a-6e98-11f0-b155-fa87dbeceb7a"} (进度: 100%)
2025-08-01 13:26:29 线程3：[信息] [信息] 第一页第1次识别结果: 3n2884 → 转换为小写: 3n2884 (进度: 100%)
2025-08-01 13:26:29 线程3：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 13:26:29 线程3：[信息] [信息] 已填入验证码: 3n2884 (进度: 100%)
2025-08-01 13:26:29 线程3：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 13:26:29 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 13:26:29 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 13:26:29
2025-08-01 13:26:29 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 13:26:29 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 13:26:29
2025-08-01 13:26:31 线程3：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 13:26:31 线程3：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 13:26:31 线程3：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 13:26:31 线程3：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 13:26:31 [信息] [线程2] 邮箱验证码获取成功: 418857，立即停止重复请求
2025-08-01 13:26:31 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-01 13:26:31 [信息] [线程2] 已清理响应文件
2025-08-01 13:26:31 线程2：[信息] [信息] 验证码获取成功: 418857，正在自动填入... (进度: 25%)
2025-08-01 13:26:31 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-01 13:26:31 线程3：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 13:26:31 线程3：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 13:26:31 线程3：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 13:26:31 线程3：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 13:26:31 线程3：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 13:26:31 [信息] [线程3] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 13:26:31 [信息] [线程3] 已删除旧的响应文件
2025-08-01 13:26:31 [信息] [线程3] 等待2秒后开始第一次触发...
2025-08-01 13:26:31 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-01 13:26:31 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-01 13:26:31 [信息] 线程2完成第二页事件已处理
2025-08-01 13:26:31 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-01 13:26:31 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-01 13:26:31 [信息] 开始批量获取3个手机号码，服务商: Durian
2025-08-01 13:26:31 [信息] [手机API] 批量获取3个手机号码，URL: https://api.durianrcs.com/out/ext_api/getMobile?name=oneone&ApiKey=NnlaR01xQm9hMlAwRnJDSVB2SG1kQT09&cuy=mx&pid=0209&num=3&noblack=0&serial=2&secret_key=null&vip=null
2025-08-01 13:26:31 [信息] [线程1] 邮箱验证码获取成功: 678781，立即停止重复请求
2025-08-01 13:26:31 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-01 13:26:31 [信息] [线程1] 已清理响应文件
2025-08-01 13:26:31 线程1：[信息] [信息] 验证码获取成功: 678781，正在自动填入... (进度: 25%)
2025-08-01 13:26:31 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-01 13:26:31 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-01 13:26:32 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-01 13:26:32 [信息] 线程1完成第二页事件已处理
2025-08-01 13:26:32 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-01 13:26:32 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-01 13:26:32 [信息] [手机API] 批量获取响应内容: {"code":200,"msg":"Success","data":["+523339014702","+524494904401","+527321372876"]}
2025-08-01 13:26:32 [信息] [手机API] 检测到数组格式，元素数量: 3
2025-08-01 13:26:32 [信息] [手机API] 批量获取成功，获得3个手机号码
2025-08-01 13:26:32 [信息] 线程1分配榴莲手机号码: +523339014702
2025-08-01 13:26:32 [信息] 线程2分配榴莲手机号码: +524494904401
2025-08-01 13:26:32 [信息] 线程3分配榴莲手机号码: +527321372876
2025-08-01 13:26:32 [信息] 榴莲API批量获取手机号码成功，已分配给3个线程
2025-08-01 13:26:32 [信息] 批量获取3个手机号码成功
2025-08-01 13:26:33 [信息] [线程3] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 13:26:33 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 13:26:33
2025-08-01 13:26:34 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-01 13:26:34 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-01 13:26:34 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 13:26:34 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 13:26:34 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-01 13:26:35 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-01 13:26:35 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-01 13:26:35 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 13:26:35 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 13:26:35 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-01 13:26:35 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-01 13:26:36 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-01 13:26:36 [信息] [线程3] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 13:26:36 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 13:26:36
2025-08-01 13:26:38 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-01 13:26:38 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-01 13:26:38 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-01 13:26:39 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-01 13:26:39 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-01 13:26:39 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-01 13:26:39 [信息] [线程3] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 13:26:39 [信息] [线程3] 已写入请求文件: ThreadId:3|Email:<EMAIL>|Time:2025-08-01 13:26:39
2025-08-01 13:26:39 [信息] [线程3] 邮箱验证码获取成功: 545545，立即停止重复请求
2025-08-01 13:26:39 [信息] [线程3] 已清理请求文件，停止重复触发
2025-08-01 13:26:39 [信息] [线程3] 已清理响应文件
2025-08-01 13:26:39 线程3：[信息] [信息] 验证码获取成功: 545545，正在自动填入... (进度: 25%)
2025-08-01 13:26:39 线程3：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-01 13:26:39 线程3：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-01 13:26:39 线程3：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-01 13:26:39 [信息] 线程3完成第二页事件已处理
2025-08-01 13:26:39 [信息] 线程3完成第二页，手机号码已获取，无需重复获取
2025-08-01 13:26:39 线程3：[信息] [信息] 线程3第二页验证完成，已通知管理器 (进度: 35%)
2025-08-01 13:26:42 线程3：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-01 13:26:42 线程3：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-01 13:26:42 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-01 13:26:42 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-01 13:26:43 线程3：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 13:26:43 线程3：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 13:26:43 线程3：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-01 13:26:44 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-01 13:26:44 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-01 13:26:44 线程3：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-01 13:26:49 线程3：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-01 13:26:49 线程3：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-01 13:26:49 线程3：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-01 13:26:52 线程3：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-01 13:26:52 线程3：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-01 13:26:55 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-01 13:26:55 [信息] [调试] 线程2请求手机号码，当前服务商: Durian
2025-08-01 13:26:55 [信息] [调试] 榴莲API字典中的线程数量: 3
2025-08-01 13:26:55 [信息] [调试] 千川API字典中的线程数量: 0
2025-08-01 13:26:55 [信息] [调试] 美国API字典中的线程数量: 0
2025-08-01 13:26:55 [信息] [调试] 检查线程2是否在榴莲API字典中...
2025-08-01 13:26:55 [信息] [调试] 找到线程2的榴莲手机号码: +524494904401
2025-08-01 13:26:55 [信息] 线程2获取已分配的榴莲手机号码: +524494904401
2025-08-01 13:26:55 线程2：[信息] [信息] 多线程模式：使用已分配的手机号码 +524494904401 (进度: 38%)
2025-08-01 13:26:55 线程2：[信息] [信息] 数据国家代码为AZ，需要选择Azerbaijan (进度: 38%)
2025-08-01 13:26:55 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-01 13:26:57 线程2：[信息] [信息] 已选择国家: Azerbaijan (进度: 38%)
2025-08-01 13:26:57 线程2：[信息] [信息] 已成功选择国家: Azerbaijan (进度: 38%)
2025-08-01 13:26:57 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-01 13:26:57 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 13:26:58 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-01 13:26:58 [信息] [调试] 线程1请求手机号码，当前服务商: Durian
2025-08-01 13:26:58 [信息] [调试] 榴莲API字典中的线程数量: 3
2025-08-01 13:26:58 [信息] [调试] 千川API字典中的线程数量: 0
2025-08-01 13:26:58 [信息] [调试] 美国API字典中的线程数量: 0
2025-08-01 13:26:58 [信息] [调试] 检查线程1是否在榴莲API字典中...
2025-08-01 13:26:58 [信息] [调试] 找到线程1的榴莲手机号码: +523339014702
2025-08-01 13:26:58 [信息] 线程1获取已分配的榴莲手机号码: +523339014702
2025-08-01 13:26:58 线程1：[信息] [信息] 多线程模式：使用已分配的手机号码 +523339014702 (进度: 38%)
2025-08-01 13:26:58 线程1：[信息] [信息] 数据国家代码为AZ，需要选择Azerbaijan (进度: 38%)
2025-08-01 13:26:58 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-01 13:27:00 线程2：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-01 13:27:00 线程1：[信息] [信息] 已选择国家: Azerbaijan (进度: 38%)
2025-08-01 13:27:00 线程1：[信息] [信息] 已成功选择国家: Azerbaijan (进度: 38%)
2025-08-01 13:27:00 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-01 13:27:00 线程2：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-01 13:27:00 线程2：[信息] [信息] 已自动获取并填入手机号码: +524494904401 (进度: 38%)
2025-08-01 13:27:00 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 13:27:01 线程2：[信息] [信息] 使用已获取的手机号码: +524494904401（保存本地号码: +524494904401） (进度: 38%)
2025-08-01 13:27:01 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-01 13:27:04 线程1：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-01 13:27:04 线程2：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-01 13:27:05 线程1：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-01 13:27:05 线程1：[信息] [信息] 已自动获取并填入手机号码: +523339014702 (进度: 38%)
2025-08-01 13:27:05 线程2：[信息] [信息] 正在选择月份: August (进度: 38%)
2025-08-01 13:27:06 线程2：[信息] [信息] 已选择月份（标准选项）: August (进度: 38%)
2025-08-01 13:27:06 线程1：[信息] [信息] 使用已获取的手机号码: +523339014702（保存本地号码: +523339014702） (进度: 38%)
2025-08-01 13:27:06 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-01 13:27:06 线程2：[信息] [信息] 正在选择年份: 2025 (进度: 38%)
2025-08-01 13:27:06 线程2：[信息] [信息] 已选择年份（标准选项）: 2025 (进度: 38%)
2025-08-01 13:27:07 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-01 13:27:07 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-01 13:27:07 线程2：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-01 13:27:08 线程3：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-01 13:27:08 [信息] [调试] 线程3请求手机号码，当前服务商: Durian
2025-08-01 13:27:08 [信息] [调试] 榴莲API字典中的线程数量: 3
2025-08-01 13:27:08 [信息] [调试] 千川API字典中的线程数量: 0
2025-08-01 13:27:08 [信息] [调试] 美国API字典中的线程数量: 0
2025-08-01 13:27:08 [信息] [调试] 检查线程3是否在榴莲API字典中...
2025-08-01 13:27:08 [信息] [调试] 找到线程3的榴莲手机号码: +527321372876
2025-08-01 13:27:08 [信息] 线程3获取已分配的榴莲手机号码: +527321372876
2025-08-01 13:27:08 线程3：[信息] [信息] 多线程模式：使用已分配的手机号码 +527321372876 (进度: 38%)
2025-08-01 13:27:09 线程3：[信息] [信息] 数据国家代码为AZ，需要选择Azerbaijan (进度: 38%)
2025-08-01 13:27:09 线程3：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-01 13:27:09 线程1：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-01 13:27:10 线程1：[信息] [信息] 正在选择月份: August (进度: 38%)
2025-08-01 13:27:10 线程3：[信息] [信息] 已选择国家: Azerbaijan (进度: 38%)
2025-08-01 13:27:10 线程3：[信息] [信息] 已成功选择国家: Azerbaijan (进度: 38%)
2025-08-01 13:27:10 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-01 13:27:11 线程1：[信息] [信息] 已选择月份（标准选项）: August (进度: 38%)
2025-08-01 13:27:11 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 13:27:11 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 13:27:11 线程1：[信息] [信息] 正在选择年份: 2026 (进度: 38%)
2025-08-01 13:27:11 线程1：[信息] [信息] 已选择年份（标准选项）: 2026 (进度: 38%)
2025-08-01 13:27:12 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-01 13:27:12 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-01 13:27:12 线程1：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-01 13:27:12 线程2：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-01 13:27:12 线程2：[信息] [信息] 已清空并重新填写手机号码: +524494904401 (进度: 38%)
2025-08-01 13:27:13 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-01 13:27:14 线程3：[信息] [信息] 已选择国家代码 +52 (进度: 38%)
2025-08-01 13:27:15 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-01 13:27:15 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-01 13:27:15 线程2：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-01 13:27:15 线程2：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-01 13:27:15 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-01 13:27:15 线程3：[信息] [信息] 等待后台获取的手机号码结果... (进度: 38%)
2025-08-01 13:27:15 线程3：[信息] [信息] 已自动获取并填入手机号码: +527321372876 (进度: 38%)
2025-08-01 13:27:16 线程3：[信息] [信息] 使用已获取的手机号码: +527321372876（保存本地号码: +527321372876） (进度: 38%)
2025-08-01 13:27:16 线程3：[信息] [信息] 联系信息完成，等待页面加载... (进度: 38%)
2025-08-01 13:27:16 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 13:27:17 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-01 13:27:17 线程1：[信息] [信息] 已清空并重新填写手机号码: +523339014702 (进度: 38%)
2025-08-01 13:27:17 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-01 13:27:18 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-01 13:27:18 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 13:27:19 线程3：[信息] [信息] 进入付款信息页面... (进度: 38%)
2025-08-01 13:27:19 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-01 13:27:19 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-01 13:27:19 线程1：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-01 13:27:19 线程1：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-01 13:27:19 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-01 13:27:20 线程3：[信息] [信息] 正在选择月份: July (进度: 38%)
2025-08-01 13:27:20 线程3：[信息] [信息] 已选择月份（标准选项）: July (进度: 38%)
2025-08-01 13:27:21 线程3：[信息] [信息] 正在选择年份: 2026 (进度: 38%)
2025-08-01 13:27:21 线程3：[信息] [信息] 已选择年份（标准选项）: 2026 (进度: 38%)
2025-08-01 13:27:22 线程3：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 38%)
2025-08-01 13:27:22 线程3：[信息] [信息] 开始填写验证码验证页面... (进度: 38%)
2025-08-01 13:27:22 线程3：[信息] [信息] 正在选择国家代码 +52 (墨西哥 (Mexico) +52)... (进度: 38%)
2025-08-01 13:27:22 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-01 13:27:22 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 13:27:23 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35014 字节 (进度: 100%)
2025-08-01 13:27:23 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，35014字节，复杂度符合要求 (进度: 100%)
2025-08-01 13:27:23 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 13:27:24 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"6trgxm"},"taskId":"341db8f2-6e98-11f0-bc80-aef288a9174b"} (进度: 100%)
2025-08-01 13:27:24 线程2：[信息] [信息] 第六页第1次识别结果: 6trgxm → 转换为小写: 6trgxm (进度: 100%)
2025-08-01 13:27:24 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 13:27:24 线程2：[信息] [信息] 第六页已填入验证码: 6trgxm (进度: 100%)
2025-08-01 13:27:24 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 13:27:26 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35390 字节 (进度: 100%)
2025-08-01 13:27:26 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，35390字节，复杂度符合要求 (进度: 100%)
2025-08-01 13:27:26 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 13:27:26 线程3：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 13:27:27 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"zy3szc"},"taskId":"35cbb9f6-6e98-11f0-931b-9625d22c4f74"} (进度: 100%)
2025-08-01 13:27:27 线程1：[信息] [信息] 第六页第1次识别结果: zy3szc → 转换为小写: zy3szc (进度: 100%)
2025-08-01 13:27:27 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 13:27:27 线程1：[信息] [信息] 第六页已填入验证码: zy3szc (进度: 100%)
2025-08-01 13:27:27 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 13:27:27 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-01 13:27:27 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-01 13:27:27 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 38%)
2025-08-01 13:27:27 线程3：[信息] [信息] 已清空并重新填写手机号码: +527321372876 (进度: 38%)
2025-08-01 13:27:28 线程3：[信息] [信息] 已点击发送验证码按钮 (进度: 38%)
2025-08-01 13:27:30 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 38%)
2025-08-01 13:27:30 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-01 13:27:30 线程3：[信息] [信息] 手机号码自动模式 + 图形验证码自动模式：开始自动处理... (进度: 100%)
2025-08-01 13:27:30 线程3：[信息] [信息] 自动模式：开始处理图形验证码... (进度: 100%)
2025-08-01 13:27:30 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-01 13:27:30 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-01 13:27:30 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-01 13:27:31 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-01 13:27:33 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-01 13:27:33 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 13:27:33 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-01 13:27:34 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-01 13:27:34 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-01 13:27:34 线程2：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-01 13:27:34 线程2：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-01 13:27:36 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35042 字节 (进度: 100%)
2025-08-01 13:27:36 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，35042字节，复杂度符合要求 (进度: 100%)
2025-08-01 13:27:36 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 13:27:36 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-01 13:27:36 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-01 13:27:36 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-01 13:27:36 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-01 13:27:37 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"xdsbzm"},"taskId":"3bfca254-6e98-11f0-bffc-fa87dbeceb7a"} (进度: 100%)
2025-08-01 13:27:37 线程3：[信息] [信息] 第六页第1次识别结果: xdsbzm → 转换为小写: xdsbzm (进度: 100%)
2025-08-01 13:27:37 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 13:27:37 线程3：[信息] [信息] 第六页已填入验证码: xdsbzm (进度: 100%)
2025-08-01 13:27:37 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 13:27:39 线程2：[信息] [信息] 线程2开始独立获取验证码... (进度: 100%)
2025-08-01 13:27:39 线程2：[信息] [信息] 线程2开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-01 13:27:39 线程2：[信息] [信息] 线程2第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-01 13:27:39 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:27:40 线程2：[信息] [信息] 线程2验证码获取成功: 9793 (进度: 100%)
2025-08-01 13:27:40 [信息] 线程2手机号码已加入释放队列: +524494904401 (原因: 获取验证码成功)
2025-08-01 13:27:40 线程2：[信息] [信息] 线程2验证码获取成功: 9793，立即填入验证码... (进度: 100%)
2025-08-01 13:27:40 线程2：[信息] [信息] 线程2已自动填入手机验证码: 9793 (进度: 100%)
2025-08-01 13:27:40 线程3：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-01 13:27:40 线程3：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-01 13:27:40 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-01 13:27:40 [信息] 开始释放1个手机号码
2025-08-01 13:27:40 [信息] [手机API] 开始批量释放1个手机号码
2025-08-01 13:27:40 [信息] [手机API] 释放手机号码: +524494904401
2025-08-01 13:27:41 线程2：[信息] [信息] 线程2正在自动点击Continue按钮... (进度: 100%)
2025-08-01 13:27:41 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-01 13:27:41 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-01 13:27:41 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-01 13:27:41 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:27:41 线程2：[信息] [信息] 线程2手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-01 13:27:41 [信息] [手机API] 手机号码释放成功: +524494904401
2025-08-01 13:27:41 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:27:41 线程1：[信息] [信息] 线程1第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:27:41 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:27:42 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-01 13:27:42 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-01 13:27:43 线程3：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-01 13:27:44 线程2：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-08-01 13:27:44 线程2：[信息] [信息] 线程2检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-08-01 13:27:44 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：ckRU1A1Eg ③AWS密码：7fC5Xmge ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-08-01 13:27:44 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：ckRU1A1Eg ③AWS密码：7fC5Xmge ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-01 13:27:44 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：ckRU1A1Eg ③AWS密码：7fC5Xmge ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-01 13:27:44 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：ckRU1A1Eg ③AWS密码：7fC5Xmge ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-01 13:27:44 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：ckRU1A1Eg ③AWS密码：7fC5Xmge ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-01 13:27:44 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：ckRU1A1Eg ③AWS密码：7fC5Xmge ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-01 13:27:44 线程2：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-08-01 13:27:44 [信息] 线程2请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-08-01 13:27:44 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-01 13:27:44 [信息] 线程2失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-08-01 13:27:44 线程2：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-08-01 13:27:44 线程2：[信息] [信息] 线程2注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-08-01 13:27:46 线程3：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-01 13:27:46 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-01 13:27:46 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-01 13:27:46 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-01 13:27:49 线程1：[信息] [信息] 线程1第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-08-01 13:27:49 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:27:50 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:27:50 线程1：[信息] [信息] 线程1第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:27:50 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:27:51 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-01 13:27:51 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-01 13:27:51 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-01 13:27:51 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:27:52 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:27:52 线程3：[信息] [信息] 线程3第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:27:52 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:27:58 线程1：[信息] [信息] 线程1第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-08-01 13:27:58 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:27:58 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:27:58 线程1：[信息] [信息] 线程1第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:27:58 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:28:00 线程3：[信息] [信息] 线程3第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-08-01 13:28:00 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:28:00 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:28:00 线程3：[信息] [信息] 线程3第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:28:00 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:28:06 线程1：[信息] [信息] 线程1第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-08-01 13:28:06 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:28:06 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:28:06 线程1：[信息] [信息] 线程1第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:28:06 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:28:08 线程3：[信息] [信息] 线程3第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-08-01 13:28:08 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:28:09 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:28:09 线程3：[信息] [信息] 线程3第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:28:09 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:28:14 线程1：[信息] [信息] 线程1第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-08-01 13:28:14 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:28:15 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:28:15 线程1：[信息] [信息] 线程1第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:28:15 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:28:17 线程3：[信息] [信息] 线程3第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-08-01 13:28:17 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:28:17 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:28:17 线程3：[信息] [信息] 线程3第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:28:17 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:28:23 线程1：[信息] [信息] 线程1第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-08-01 13:28:23 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:28:23 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:28:23 线程1：[信息] [信息] 线程1第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:28:23 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:28:25 线程3：[信息] [信息] 线程3第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-08-01 13:28:25 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:28:25 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:28:25 线程3：[信息] [信息] 线程3第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:28:25 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:28:31 线程1：[信息] [信息] 线程1第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-08-01 13:28:31 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:28:31 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:28:31 线程1：[信息] [信息] 线程1第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:28:31 线程1：[信息] [信息] 线程1等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:28:33 线程3：[信息] [信息] 线程3第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-08-01 13:28:33 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:28:34 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:28:34 线程3：[信息] [信息] 线程3第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:28:34 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:28:39 线程1：[信息] [信息] 线程1第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-08-01 13:28:39 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:28:40 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:28:40 线程1：[信息] [信息] 线程1第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:28:40 线程1：[信息] [信息] 线程1验证码获取失败，已尝试8次 (进度: 100%)
2025-08-01 13:28:40 [信息] 线程1手机号码已加入释放队列: +523339014702 (原因: 验证码获取失败)
2025-08-01 13:28:40 线程1：[信息] [信息] 线程1验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-08-01 13:28:40 线程1：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-08-01 13:28:40 线程1：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-08-01 13:28:40 线程1：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-08-01 13:28:41 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-01 13:28:41 [信息] 开始释放1个手机号码
2025-08-01 13:28:41 [信息] [手机API] 开始批量释放1个手机号码
2025-08-01 13:28:41 [信息] [手机API] 释放手机号码: +523339014702
2025-08-01 13:28:43 [信息] [手机API] 手机号码释放成功: +523339014702
2025-08-01 13:28:43 线程1：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-08-01 13:28:43 线程3：[信息] [信息] 线程3第7次尝试获取验证码...（剩余1次尝试） (进度: 100%)
2025-08-01 13:28:43 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:28:43 线程1：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-08-01 13:28:43 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:28:43 线程3：[信息] [信息] 线程3第7次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:28:43 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:28:43 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-01 13:28:43 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-01 13:28:46 线程1：[信息] [信息] 正在选择国家代码: mx (进度: 100%)
2025-08-01 13:28:46 线程1：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-08-01 13:28:47 线程1：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-01 13:28:47 线程1：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-08-01 13:28:47 [信息] [榴莲API] 获取手机号码，尝试 1/4
2025-08-01 13:28:47 线程1：[信息] [信息] 后台获取新手机号码成功: +527862473645，已保存到注册数据 (进度: 100%)
2025-08-01 13:28:48 线程1：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-08-01 13:28:48 线程1：[信息] [信息] 已填入新手机号码: +527862473645 (进度: 100%)
2025-08-01 13:28:48 线程1：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-08-01 13:28:50 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-01 13:28:50 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-01 13:28:51 线程1：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-08-01 13:28:51 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-01 13:28:51 线程3：[信息] [信息] 线程3第8次尝试获取验证码...（剩余0次尝试） (进度: 100%)
2025-08-01 13:28:51 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:28:51 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:28:51 线程3：[信息] [信息] 线程3第8次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:28:51 线程3：[信息] [信息] 线程3验证码获取失败，已尝试8次 (进度: 100%)
2025-08-01 13:28:51 [信息] 线程3手机号码已加入释放队列: +527321372876 (原因: 验证码获取失败)
2025-08-01 13:28:51 线程3：[信息] [信息] 线程3验证码获取失败: 获取失败，已尝试8次 (进度: 100%)
2025-08-01 13:28:51 线程3：[信息] [信息] 验证码获取失败，第1次重试... (进度: 100%)
2025-08-01 13:28:51 线程3：[信息] [信息] 正在自动重试第1次，同时加入黑名单和获取新号码... (进度: 100%)
2025-08-01 13:28:51 线程3：[信息] [信息] 正在返回上一页... (进度: 100%)
2025-08-01 13:28:51 线程3：[信息] [信息] 已点击返回按钮 (进度: 100%)
2025-08-01 13:28:52 线程3：[信息] [信息] 超时号码已加入黑名单 (进度: 100%)
2025-08-01 13:28:54 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-01 13:28:54 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 13:28:54 线程3：[信息] [信息] 正在选择国家代码: mx (进度: 100%)
2025-08-01 13:28:55 线程3：[信息] [信息] 已打开国家代码下拉列表 (进度: 100%)
2025-08-01 13:28:56 线程3：[信息] [信息] 已选择国家代码: +52 (进度: 100%)
2025-08-01 13:28:56 线程3：[信息] [信息] 后台获取新手机号码... (进度: 100%)
2025-08-01 13:28:56 [信息] [榴莲API] 获取手机号码，尝试 1/4
2025-08-01 13:28:56 线程3：[信息] [信息] 后台获取新手机号码成功: +528332911922，已保存到注册数据 (进度: 100%)
2025-08-01 13:28:56 线程3：[信息] [信息] 正在清空并填入新的手机号码... (进度: 100%)
2025-08-01 13:28:56 线程3：[信息] [信息] 已填入新手机号码: +528332911922 (进度: 100%)
2025-08-01 13:28:56 线程3：[信息] [信息] 正在点击发送短信按钮... (进度: 100%)
2025-08-01 13:28:57 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34683 字节 (进度: 100%)
2025-08-01 13:28:57 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，34683字节，复杂度符合要求 (进度: 100%)
2025-08-01 13:28:57 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 13:28:58 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"8gtb5x"},"taskId":"6c3f28b0-6e98-11f0-9c52-aef288a9174b"} (进度: 100%)
2025-08-01 13:28:58 线程1：[信息] [信息] 第六页第1次识别结果: 8gtb5x → 转换为小写: 8gtb5x (进度: 100%)
2025-08-01 13:28:58 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 13:28:58 线程1：[信息] [信息] 第六页已填入验证码: 8gtb5x (进度: 100%)
2025-08-01 13:28:58 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 13:28:59 线程3：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-01 13:28:59 线程3：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-01 13:29:00 线程3：[信息] [信息] 新手机号码已填入，自动模式：开始处理图形验证码... (进度: 100%)
2025-08-01 13:29:00 线程3：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-01 13:29:01 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-01 13:29:01 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-01 13:29:03 线程3：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-01 13:29:03 线程3：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 13:29:06 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-01 13:29:06 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 34794 字节 (进度: 100%)
2025-08-01 13:29:06 线程3：[信息] [信息] ✅ 图片验证通过：201x70px，34794字节，复杂度符合要求 (进度: 100%)
2025-08-01 13:29:06 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 13:29:07 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"pm6g4x"},"taskId":"71a2c65e-6e98-11f0-931b-9625d22c4f74"} (进度: 100%)
2025-08-01 13:29:07 线程3：[信息] [信息] 第六页第1次识别结果: pm6g4x → 转换为小写: pm6g4x (进度: 100%)
2025-08-01 13:29:07 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 13:29:07 线程3：[信息] [信息] 第六页已填入验证码: pm6g4x (进度: 100%)
2025-08-01 13:29:08 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 13:29:09 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-01 13:29:09 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-01 13:29:09 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-01 13:29:09 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-01 13:29:10 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-01 13:29:10 [信息] 开始释放1个手机号码
2025-08-01 13:29:10 [信息] [手机API] 开始批量释放1个手机号码
2025-08-01 13:29:10 [信息] [手机API] 释放手机号码: +527321372876
2025-08-01 13:29:11 [信息] [手机API] 手机号码释放成功: +527321372876
2025-08-01 13:29:11 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-01 13:29:11 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-01 13:29:12 线程3：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-01 13:29:12 线程3：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-01 13:29:14 线程3：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 13:29:14 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-08-01 13:29:14 线程1：[信息] [信息] 线程1开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-01 13:29:14 线程1：[信息] [信息] 线程1第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-01 13:29:14 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:29:14 线程1：[信息] [信息] 线程1验证码获取成功: 2689 (进度: 100%)
2025-08-01 13:29:14 [信息] 线程1手机号码已加入释放队列: +527862473645 (原因: 获取验证码成功)
2025-08-01 13:29:14 线程1：[信息] [信息] 线程1验证码获取成功: 2689，立即填入验证码... (进度: 100%)
2025-08-01 13:29:14 线程1：[信息] [信息] 线程1已自动填入手机验证码: 2689 (进度: 100%)
2025-08-01 13:29:15 线程1：[信息] [信息] 线程1正在自动点击Continue按钮... (进度: 100%)
2025-08-01 13:29:15 线程1：[信息] [信息] 线程1手机验证码验证完成，继续执行后续步骤... (进度: 100%)
2025-08-01 13:29:21 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31287 字节 (进度: 100%)
2025-08-01 13:29:21 线程3：[信息] [信息] ✅ 图片验证通过：200x70px，31287字节，复杂度符合要求 (进度: 100%)
2025-08-01 13:29:21 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 13:29:21 线程1：[信息] [信息] 检测到无资格错误提示: You are not eligible for new customer credits (进度: 100%)
2025-08-01 13:29:21 线程1：[信息] [信息] 线程1检测到卡号已被关联错误，注册失败 (进度: 100%)
2025-08-01 13:29:21 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：KEyw2F6i2 ③AWS密码：Ipneb7BD ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联 (进度: 100%)
2025-08-01 13:29:21 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：KEyw2F6i2 ③AWS密码：Ipneb7BD ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-01 13:29:21 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：KEyw2F6i2 ③AWS密码：Ipneb7BD ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-01 13:29:21 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：KEyw2F6i2 ③AWS密码：Ipneb7BD ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-01 13:29:21 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：KEyw2F6i2 ③AWS密码：Ipneb7BD ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-01 13:29:21 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：KEyw2F6i2 ③AWS密码：Ipneb7BD ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //卡号已被关联
2025-08-01 13:29:21 线程1：[信息] 收到失败数据保存请求: 卡号已被关联, 数据: <EMAIL>
2025-08-01 13:29:21 [信息] 线程1请求保存失败数据: 卡号已被关联, 数据: <EMAIL>
2025-08-01 13:29:21 [信息] 已处理失败数据: <EMAIL>, 失败原因: 卡号已被关联
2025-08-01 13:29:21 [信息] 线程1失败数据已保存: 卡号已被关联, 数据: <EMAIL>
2025-08-01 13:29:21 线程1：[信息] [信息] 已通知保存失败数据，失败原因: 卡号已被关联 (进度: 0%)
2025-08-01 13:29:21 线程1：[信息] [信息] 线程1注册失败，数据已归类，注册流程终止 (进度: 0%)
2025-08-01 13:29:22 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"dm66"},"taskId":"7a55f208-6e98-11f0-b155-fa87dbeceb7a"} (进度: 100%)
2025-08-01 13:29:22 线程3：[信息] [信息] 第六页第2次识别结果: dm66 → 转换为小写: dm66 (进度: 100%)
2025-08-01 13:29:22 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 13:29:22 线程3：[信息] [信息] 第六页已填入验证码: dm66 (进度: 100%)
2025-08-01 13:29:22 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 13:29:26 线程3：[信息] [信息] 第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-01 13:29:26 线程3：[信息] [信息] 第六页第2次识别异常: 验证码错误 (进度: 100%)
2025-08-01 13:29:28 线程3：[信息] [信息] 第六页第3次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 13:29:32 线程3：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 31858 字节 (进度: 100%)
2025-08-01 13:29:32 线程3：[信息] [信息] ✅ 图片验证通过：200x70px，31858字节，复杂度符合要求 (进度: 100%)
2025-08-01 13:29:32 线程3：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 13:29:33 线程3：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"x7x7"},"taskId":"80dd0012-6e98-11f0-9d4c-420da957ad82"} (进度: 100%)
2025-08-01 13:29:33 线程3：[信息] [信息] 第六页第3次识别结果: x7x7 → 转换为小写: x7x7 (进度: 100%)
2025-08-01 13:29:33 线程3：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 13:29:33 线程3：[信息] [信息] 第六页已填入验证码: x7x7 (进度: 100%)
2025-08-01 13:29:33 线程3：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 13:29:37 线程3：[信息] [信息] 第3次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-01 13:29:37 线程3：[信息] [信息] 第六页第3次识别异常: 验证码错误 (进度: 100%)
2025-08-01 13:29:37 线程3：[信息] [信息] 当前验证码抓取出错，需要手动完成图形验证码，并点击继续注册 (进度: 100%)
2025-08-01 13:29:40 [信息] 定时检查发现1个待释放手机号码，开始批量释放
2025-08-01 13:29:40 [信息] 开始释放1个手机号码
2025-08-01 13:29:40 [信息] [手机API] 开始批量释放1个手机号码
2025-08-01 13:29:40 [信息] [手机API] 释放手机号码: +527862473645
2025-08-01 13:29:41 [信息] [手机API] 手机号码释放成功: +527862473645
2025-08-01 13:29:41 [信息] [手机API] 批量释放完成: 成功1个, 失败0个
2025-08-01 13:29:41 [信息] 定时批量释放完成: 批量释放完成: 成功1个, 失败0个
2025-08-01 13:30:18 线程3：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-08-01 13:30:18 线程3：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-01 13:30:18 线程3：[信息] [信息] 第七页手动验证码已输入，检测当前页面状态... (进度: 100%)
2025-08-01 13:30:18 线程3：[信息] [信息] 第七页：智能检测当前页面状态... (进度: 100%)
2025-08-01 13:30:18 线程3：[信息] [信息] 🔧 [DEBUG] 使用修改后的HandleStep7SmartContinue方法 - 版本2025-07-28 (进度: 100%)
2025-08-01 13:30:18 线程3：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-01 13:30:18 线程3：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-01 13:30:19 线程3：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-01 13:30:19 线程3：[信息] [信息] 🔧 [DEBUG] 第七页验证码输入框存在但未填写或长度不足 (进度: 100%)
2025-08-01 13:30:19 线程3：[信息] [信息] 🔧 [DEBUG] 输入框不符合第七页验证码特征 (进度: 100%)
2025-08-01 13:30:19 线程3：[信息] [信息] 🔧 [DEBUG] 选择器 form:has-text('verification code') input[type='text'] 找到输入框但不是第七页验证码输入框，跳过 (进度: 100%)
2025-08-01 13:30:19 线程3：[信息] [信息] 🔧 [DEBUG] 未检测到第七页手机验证码已填写 (进度: 100%)
2025-08-01 13:30:19 线程3：[信息] [信息] 第七页：检测到验证码未输入，根据配置决定自动获取还是手动输入... (进度: 100%)
2025-08-01 13:30:19 线程3：[信息] [信息] 第七页：检测到手机验证码自动模式，开始自动获取验证码流程... (进度: 100%)
2025-08-01 13:30:19 线程3：[信息] [信息] 使用已有的完整手机号码: +528332911922 (进度: 100%)
2025-08-01 13:30:19 线程3：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-08-01 13:30:19 线程3：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-08-01 13:30:24 线程3：[信息] [信息] 线程3开始独立获取验证码... (进度: 100%)
2025-08-01 13:30:24 线程3：[信息] [信息] 线程3开始获取验证码，最多尝试8次，每次间隔8秒 (进度: 100%)
2025-08-01 13:30:24 线程3：[信息] [信息] 线程3第1次尝试获取验证码...（剩余7次尝试） (进度: 100%)
2025-08-01 13:30:24 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:30:24 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:30:24 线程3：[信息] [信息] 线程3第1次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:30:24 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:30:32 线程3：[信息] [信息] 线程3第2次尝试获取验证码...（剩余6次尝试） (进度: 100%)
2025-08-01 13:30:32 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:30:33 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:30:33 线程3：[信息] [信息] 线程3第2次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:30:33 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:30:41 线程3：[信息] [信息] 线程3第3次尝试获取验证码...（剩余5次尝试） (进度: 100%)
2025-08-01 13:30:41 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:30:41 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:30:41 线程3：[信息] [信息] 线程3第3次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:30:42 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:30:50 线程3：[信息] [信息] 线程3第4次尝试获取验证码...（剩余4次尝试） (进度: 100%)
2025-08-01 13:30:50 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:30:50 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:30:50 线程3：[信息] [信息] 线程3第4次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:30:50 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:30:58 线程3：[信息] [信息] 线程3第5次尝试获取验证码...（剩余3次尝试） (进度: 100%)
2025-08-01 13:30:58 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:30:58 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:30:58 线程3：[信息] [信息] 线程3第5次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:30:58 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
2025-08-01 13:31:06 线程3：[信息] [信息] 线程3第6次尝试获取验证码...（剩余2次尝试） (进度: 100%)
2025-08-01 13:31:06 [信息] [榴莲API] 获取验证码，尝试 1/4
2025-08-01 13:31:06 [警告] [榴莲API] 获取验证码业务失败: 暂未查询到验证码，请稍后再试
2025-08-01 13:31:06 线程3：[信息] [信息] 线程3第6次尝试失败: 暂未查询到验证码，请稍后再试 (进度: 100%)
2025-08-01 13:31:06 线程3：[信息] [信息] 线程3等待8秒后进行下一次尝试... (进度: 100%)
