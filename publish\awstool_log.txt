2025-07-31 20:40:42 [信息] AWS自动注册工具启动
2025-07-31 20:40:42 [信息] 程序版本: 1.0.0.0
2025-07-31 20:40:42 [信息] 启动时间: 2025-07-31 20:40:42
2025-07-31 20:40:42 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-07-31 20:40:42 [信息] 线程数量已选择: 1
2025-07-31 20:40:42 [信息] 线程数量选择初始化完成
2025-07-31 20:40:42 [信息] 程序初始化完成
2025-07-31 20:41:10 [信息] 线程数量已选择: 2
2025-07-31 20:41:10 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-07-31 20:41:15 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-07-31-印度尼西亚.txt
2025-07-31 20:41:15 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-07-31-印度尼西亚.txt
2025-07-31 20:41:15 [信息] 成功加载 2 条数据
2025-07-31 20:41:20 [按钮操作] 开始注册 -> 启动注册流程
2025-07-31 20:41:20 [信息] 开始启动多线程注册，线程数量: 2
2025-07-31 20:41:20 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 2
2025-07-31 20:41:20 [信息] 所有线程已停止并清理
2025-07-31 20:41:20 [信息] 正在初始化多线程服务...
2025-07-31 20:41:20 [信息] 美国手机API服务已初始化
2025-07-31 20:41:20 [信息] 手机号码管理器已初始化，服务商: USA，将在第一个线程完成第二页后获取手机号码
2025-07-31 20:41:20 [信息] 多线程服务初始化完成
2025-07-31 20:41:20 [信息] 数据分配完成：共2条数据分配给2个线程
2025-07-31 20:41:20 [信息] 线程1分配到1条数据
2025-07-31 20:41:20 [信息] 线程2分配到1条数据
2025-07-31 20:41:20 [信息] 屏幕工作区域: 1280x672
2025-07-31 20:41:20 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-07-31 20:41:20 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-07-31 20:41:20 线程1：[信息] 添加数据到队列: <EMAIL>
2025-07-31 20:41:20 [信息] 线程1已创建，窗口位置: (0, 0)
2025-07-31 20:41:20 [信息] 屏幕工作区域: 1280x672
2025-07-31 20:41:20 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-07-31 20:41:20 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-07-31 20:41:20 线程2：[信息] 添加数据到队列: <EMAIL>
2025-07-31 20:41:20 [信息] 线程2已创建，窗口位置: (0, 329)
2025-07-31 20:41:20 [信息] 多线程注册启动成功，共2个线程
2025-07-31 20:41:20 线程2：[信息] 开始启动注册流程
2025-07-31 20:41:20 线程1：[信息] 开始启动注册流程
2025-07-31 20:41:20 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-07-31 20:41:20 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-07-31 20:41:20 线程1：[信息] 启动无痕Chrome浏览器...
2025-07-31 20:41:20 线程2：[信息] 启动无痕Chrome浏览器...
2025-07-31 20:41:20 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-07-31 20:41:20 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-07-31 20:41:20 [信息] 多线程管理窗口已初始化
2025-07-31 20:41:20 [信息] UniformGrid列数已更新为: 1
2025-07-31 20:41:20 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-07-31 20:41:21 [信息] 多线程管理窗口已打开
2025-07-31 20:41:21 [信息] 多线程注册启动成功，共2个线程
2025-07-31 20:41:22 [信息] UniformGrid列数已更新为: 1
2025-07-31 20:41:22 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-07-31 20:41:22 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-07-31 20:41:22 [信息] UniformGrid列数已更新为: 1
2025-07-31 20:41:22 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-07-31 20:41:22 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-07-31 20:41:22 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-07-31 20:41:22 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-07-31 20:41:22 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-07-31 20:41:22 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-07-31 20:41:22 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-07-31 20:41:22 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-07-31 20:41:23 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-07-31 20:41:23 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-07-31 20:41:26 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-07-31 20:41:26 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-07-31 20:41:27 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-07-31 20:41:27 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-07-31 20:41:27 线程1：[信息] 浏览器启动成功
2025-07-31 20:41:27 线程1：[信息] 获取下一个数据: <EMAIL>
2025-07-31 20:41:27 线程1：[信息] 开始处理账户: <EMAIL>
2025-07-31 20:41:27 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-31 20:41:27 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-07-31 20:41:27 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-07-31 20:41:27 线程1：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-07-31 20:41:27 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-07-31 20:41:27 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-07-31 20:41:27 线程2：[信息] 浏览器启动成功
2025-07-31 20:41:27 线程2：[信息] 获取下一个数据: <EMAIL>
2025-07-31 20:41:27 线程2：[信息] 开始处理账户: <EMAIL>
2025-07-31 20:41:27 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-07-31 20:41:27 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-07-31 20:41:27 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-07-31 20:41:27 线程2：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-07-31 20:41:27 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-07-31 20:41:27 [信息] 根据国家代码 ID 选择时区: Asia/Makassar
2025-07-31 20:41:27 线程1：[信息] [信息] 设置浏览器上下文语言: Bahasa Indonesia (locale: id-ID) (进度: 98%)
2025-07-31 20:41:27 [信息] 浏览器上下文语言设置: 国家代码=ID, locale=id-ID, Accept-Language=id-ID,id;q=0.9,en;q=0.8
2025-07-31 20:41:27 线程1：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-07-31 20:41:27 线程1：[信息] [信息] 设置随机时区: Asia/Makassar (进度: 98%)
2025-07-31 20:41:27 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-07-31 20:41:27 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-07-31 20:41:27 [信息] 根据国家代码 ID 选择时区: Asia/Makassar
2025-07-31 20:41:27 线程2：[信息] [信息] 设置浏览器上下文语言: Bahasa Indonesia (locale: id-ID) (进度: 98%)
2025-07-31 20:41:27 [信息] 浏览器上下文语言设置: 国家代码=ID, locale=id-ID, Accept-Language=id-ID,id;q=0.9,en;q=0.8
2025-07-31 20:41:27 线程2：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-07-31 20:41:27 线程2：[信息] [信息] 设置随机时区: Asia/Makassar (进度: 98%)
2025-07-31 20:41:27 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-07-31 20:41:28 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-07-31 20:41:28 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-07-31 20:41:28 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-07-31 20:41:28 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-07-31 20:41:28 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-07-31 20:41:28 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-07-31 20:41:28 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-07-31 20:41:28 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-07-31 20:41:28 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-07-31 20:41:28 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-07-31 20:41:28 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-07-31 20:41:28 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-07-31 20:41:45 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-07-31 20:41:45 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-07-31 20:41:45 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-07-31 20:41:45 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-07-31 20:41:45 [信息] 第一页加载完成，找到验证邮箱按钮
2025-07-31 20:41:45 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-07-31 20:41:49 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-07-31 20:41:49 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-07-31 20:41:49 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-07-31 20:41:49 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-07-31 20:41:49 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-31 20:41:49 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-07-31 20:41:49 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-07-31 20:41:49 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-07-31 20:41:49 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-07-31 20:41:49 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-31 20:41:49 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-07-31 20:41:49 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-07-31 20:41:49 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-07-31 20:41:49 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-07-31 20:41:49 [信息] 第一页加载完成，找到验证邮箱按钮
2025-07-31 20:41:49 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-07-31 20:41:52 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-07-31 20:41:52 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-07-31 20:41:52 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-07-31 20:41:52 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-07-31 20:41:52 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-31 20:41:52 线程2：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-07-31 20:41:54 线程2：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-07-31 20:41:54 线程2：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-07-31 20:41:54 线程2：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-07-31 20:41:54 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-07-31 20:41:54 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-07-31 20:41:54 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-07-31 20:41:54 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-31 20:42:02 线程1：[信息] [信息] 第一页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-07-31 20:42:02 线程1：[信息] [信息] 第一页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-07-31 20:42:02 线程1：[信息] [信息] 第一页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-07-31 20:42:07 线程2：[信息] [信息] 第一页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-07-31 20:42:07 线程2：[信息] [信息] 第一页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-07-31 20:42:07 线程2：[信息] [信息] 第一页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-07-31 20:42:23 线程1：[信息] [信息] 检测到第一页验证码已完成，继续流程 (进度: 100%)
2025-07-31 20:42:23 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-07-31 20:42:23 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-07-31 20:42:23 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-07-31 20:42:23 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-07-31 20:42:23 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-07-31 20:42:23 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-07-31 20:42:23 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-07-31 20:42:23 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-07-31 20:42:23 [信息] [线程1] 等待2秒后开始第一次触发...
2025-07-31 20:42:25 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-07-31 20:42:25 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-31 20:42:25
2025-07-31 20:42:28 线程2：[信息] [信息] 检测到第一页验证码已完成，继续流程 (进度: 100%)
2025-07-31 20:42:28 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-07-31 20:42:28 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-07-31 20:42:28 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-07-31 20:42:28 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-07-31 20:42:28 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-07-31 20:42:28 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-07-31 20:42:28 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-07-31 20:42:28 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-07-31 20:42:28 [信息] [线程2] 已删除旧的响应文件
2025-07-31 20:42:28 [信息] [线程2] 等待2秒后开始第一次触发...
2025-07-31 20:42:28 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-07-31 20:42:28 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-31 20:42:28
2025-07-31 20:42:30 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-07-31 20:42:30 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-31 20:42:30
2025-07-31 20:42:31 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-07-31 20:42:31 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-31 20:42:31
2025-07-31 20:42:33 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-07-31 20:42:33 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-31 20:42:33
2025-07-31 20:42:34 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-07-31 20:42:34 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-07-31 20:42:34
2025-07-31 20:42:34 [信息] [线程1] 邮箱验证码获取成功: 648227，立即停止重复请求
2025-07-31 20:42:34 [信息] [线程1] 已清理请求文件，停止重复触发
2025-07-31 20:42:34 [信息] [线程1] 已清理响应文件
2025-07-31 20:42:34 线程1：[信息] [信息] 验证码获取成功: 648227，正在自动填入... (进度: 25%)
2025-07-31 20:42:34 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-07-31 20:42:35 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-07-31 20:42:35 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-07-31 20:42:35 [信息] 线程1完成第二页事件已处理
2025-07-31 20:42:35 [信息] 线程1完成第二页，开始批量获取手机号码...
2025-07-31 20:42:35 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-07-31 20:42:35 [信息] 开始批量获取2个手机号码，服务商: USA
2025-07-31 20:42:35 [信息] 美国API不支持批量获取，开始逐个获取手机号码
2025-07-31 20:42:35 [信息] [美国API] 开始购买手机号码
2025-07-31 20:42:35 [信息] [美国API] 请求购买手机号码: https://www.api21k.com/api/v1/buy/create
2025-07-31 20:42:36 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-07-31 20:42:36 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-31 20:42:36
2025-07-31 20:42:38 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-07-31 20:42:38 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-07-31 20:42:38 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-31 20:42:38 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-31 20:42:38 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-07-31 20:42:39 [信息] [线程2] 第4次触发邮箱验证码获取...（最多20次）
2025-07-31 20:42:39 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-07-31 20:42:39
2025-07-31 20:42:39 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-07-31 20:42:40 [信息] [美国API] 购买响应: {"code":1,"msg":"购买成功","time":1753965758,"data":{"ordernum":"25073120423954116","api_count":1}}
2025-07-31 20:42:40 [信息] [美国API] 购买成功，订单号: 25073120423954116
2025-07-31 20:42:40 [信息] [美国API] 请求获取手机号码详情: https://www.api21k.com/api/v1/order/api
2025-07-31 20:42:41 [信息] [美国API] 获取详情响应: {"code":1,"msg":"获取订单API列表成功","time":1753965760,"data":{"url_list":["https:\/\/api.sms8.net","https:\/\/api.smsapi.cc","http:\/\/47.76.194.115"],"list":[{"id":56614033,"app_id":81,"cate_id":2,"type":1,"tel":"12568617699","token":"b3g9czplsfaemkp7zgkktfkwuhu1584nrb5b","end_time":"2025-09-30 00:00:00","sms_count":0,"voice_count":0,"remark":"","status":0,"api":"https:\/\/api.sms8.net\/api\/record?token=b3g9czplsfaemkp7zgkktfkwuhu1584nrb5b"}],"total":1}}
2025-07-31 20:42:41 [信息] [美国API] 获取手机号码成功: +12568617699
2025-07-31 20:42:41 [信息] 线程1分配美国手机号码: +12568617699
2025-07-31 20:42:42 [信息] [线程2] 邮箱验证码获取成功: 192535，立即停止重复请求
2025-07-31 20:42:42 [信息] [线程2] 已清理请求文件，停止重复触发
2025-07-31 20:42:42 [信息] [线程2] 已清理响应文件
2025-07-31 20:42:42 线程2：[信息] [信息] 验证码获取成功: 192535，正在自动填入... (进度: 25%)
2025-07-31 20:42:42 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-07-31 20:42:42 [信息] [美国API] 开始购买手机号码
2025-07-31 20:42:42 [信息] [美国API] 请求购买手机号码: https://www.api21k.com/api/v1/buy/create
2025-07-31 20:42:42 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-07-31 20:42:42 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-07-31 20:42:42 [信息] 线程2完成第二页事件已处理
2025-07-31 20:42:42 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-07-31 20:42:42 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-07-31 20:42:42 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-07-31 20:42:42 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-07-31 20:42:42 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-07-31 20:42:42 [信息] [美国API] 购买响应: {"code":0,"msg":"请勿重复提交订单","time":1753965761,"data":null}
2025-07-31 20:42:42 [警告] [美国API] 购买失败: {"code":0,"msg":"请勿重复提交订单","time":1753965761,"data":null}
2025-07-31 20:42:42 [错误] 线程2获取美国手机号码失败: 购买失败
2025-07-31 20:42:42 [错误] 美国API获取手机号码失败，成功1个，需要2个
2025-07-31 20:42:42 [警告] 批量获取手机号码失败，各线程将使用单独获取模式
2025-07-31 20:42:45 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-07-31 20:42:45 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-07-31 20:42:45 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-31 20:42:45 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-07-31 20:42:45 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-07-31 20:42:46 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-07-31 20:42:49 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-07-31 20:42:49 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-07-31 20:42:49 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-07-31 20:42:50 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-07-31 20:42:50 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-07-31 20:42:54 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-07-31 20:43:07 线程2：[信息] [信息] 第3.5页执行失败: Timeout 15000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Choose paid plan" }) to be visible (进度: 38%)
2025-07-31 20:43:24 线程1：[信息] [信息] 第3.5页执行失败: 第四页执行失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Radio, new() { Name = "Business - for your work, school, or organization" }) (进度: 48%)
2025-07-31 20:46:37 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 38%)
2025-07-31 20:46:37 线程2：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-07-31 20:46:37 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-07-31 20:46:37 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-07-31 20:46:37 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Choose paid plan' → 第3页 (进度: 38%)
2025-07-31 20:46:37 线程2：[信息] [信息] 检测到第3.5页（账户类型确认页面），直接点击Choose paid plan按钮... (进度: 38%)
2025-07-31 20:46:38 线程2：[信息] [信息] 账户类型确认完成，进入第4页（联系信息页面）... (进度: 38%)
2025-07-31 20:46:41 线程2：[信息] [信息] 检测到注册已暂停或终止，停止后续操作 (进度: 38%)
2025-07-31 20:46:41 线程2：[信息] [信息] ⚠️ 智能检测失败，启用详细页面分析... (进度: 38%)
2025-07-31 20:46:41 线程2：[信息] [信息] 🔬 执行详细页面分析... (进度: 38%)
2025-07-31 20:46:41 线程2：[信息] [信息] 📄 页面URL: https://portal.aws.amazon.com/billing/signup?type=register#/accountplan (进度: 38%)
2025-07-31 20:46:41 线程2：[信息] [信息] 📋 页面标题: AWS Console - Signup (进度: 38%)
2025-07-31 20:46:41 线程2：[信息] [信息] 📊 分析结果: 第3.5页-账户类型确认(2/3个元素匹配) (进度: 38%)
2025-07-31 20:46:41 线程2：[信息] [信息] ❌ 详细分析也无法识别页面 (进度: 38%)
2025-07-31 20:46:41 线程2：[信息] [信息] 智能检测到当前在第3页，开始智能处理... (进度: 38%)
2025-07-31 20:46:42 线程2：[信息] [信息] 检测到第3.5页（账户类型确认页面），开始处理... (进度: 38%)
2025-07-31 20:46:42 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-07-31 20:47:02 线程1：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 4 (进度: 48%)
2025-07-31 20:47:02 线程1：[信息] [信息]  进行智能页面检测... (进度: 48%)
2025-07-31 20:47:02 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 48%)
2025-07-31 20:47:02 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 48%)
2025-07-31 20:47:03 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 48%)
2025-07-31 20:47:03 线程1：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-07-31 20:47:03 线程1：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-07-31 20:47:03 线程1：[信息] [信息] 智能检测到当前在第4页，开始智能处理... (进度: 100%)
2025-07-31 20:47:05 线程1：[信息] [信息] 数据国家代码为ID，需要选择Indonesia (进度: 100%)
2025-07-31 20:47:05 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-07-31 20:47:07 线程1：[信息] [信息] 已选择国家: Indonesia (进度: 100%)
2025-07-31 20:47:07 线程1：[信息] [信息] 已成功选择国家: Indonesia (进度: 100%)
2025-07-31 20:47:07 线程1：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 100%)
2025-07-31 20:47:07 线程1：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 100%)
2025-07-31 20:47:07 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-07-31 20:47:08 线程1：[信息] [信息] 已选择国家代码 +1 (进度: 100%)
2025-07-31 20:47:08 线程1：[信息] [信息] 开始检查手机号码状态... (进度: 100%)
2025-07-31 20:47:08 线程1：[信息] [信息]  检查页面是否已有手机号码填写... (进度: 100%)
2025-07-31 20:47:08 线程1：[信息] [信息]  页面手机号码输入框当前值: '' (进度: 100%)
2025-07-31 20:47:08 线程1：[信息] [信息]  页面手机号码输入框为空，需要手动输入 (进度: 100%)
2025-07-31 20:47:08 线程1：[信息] [信息]  最终决定：显示手动输入弹窗 (进度: 100%)
2025-07-31 20:47:08 线程1：[信息] [信息] 🔴 请手动输入手机号码，然后点击【继续注册】 (进度: 100%)
2025-07-31 20:47:08 线程1：[信息] 已继续
2025-07-31 20:47:08 [信息] 线程1已继续
2025-07-31 20:47:10 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-07-31 20:47:28 线程2：[信息] [信息] 第3.5页执行失败: Timeout 15000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Choose paid plan" }) to be visible (进度: 38%)
2025-07-31 20:47:28 线程2：[信息] 已继续
2025-07-31 20:47:28 [信息] 线程2已继续
2025-07-31 20:47:45 线程2：[信息] [信息]  继续注册被调用，当前状态: Paused，当前步骤: 3 (进度: 38%)
2025-07-31 20:47:45 线程2：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-07-31 20:47:45 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-07-31 20:47:45 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-07-31 20:47:46 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 38%)
2025-07-31 20:47:46 线程2：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-07-31 20:47:46 线程2：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-07-31 20:47:46 线程2：[信息] [信息] 智能检测到当前在第4页，开始智能处理... (进度: 100%)
2025-07-31 20:47:46 线程2：[信息] [信息] 数据国家代码为ID，需要选择Indonesia (进度: 100%)
2025-07-31 20:47:46 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-07-31 20:47:48 线程2：[信息] [信息] 已选择国家: Indonesia (进度: 100%)
2025-07-31 20:47:48 线程2：[信息] [信息] 已成功选择国家: Indonesia (进度: 100%)
2025-07-31 20:47:48 线程2：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 100%)
2025-07-31 20:47:48 线程2：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 100%)
2025-07-31 20:47:48 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-07-31 20:47:50 线程2：[信息] [信息] 已选择国家代码 +1 (进度: 100%)
2025-07-31 20:47:50 线程2：[信息] [信息] 开始检查手机号码状态... (进度: 100%)
2025-07-31 20:47:50 线程2：[信息] [信息]  检查页面是否已有手机号码填写... (进度: 100%)
2025-07-31 20:47:50 线程2：[信息] [信息]  页面手机号码输入框当前值: '' (进度: 100%)
2025-07-31 20:47:50 线程2：[信息] [信息]  页面手机号码输入框为空，需要手动输入 (进度: 100%)
2025-07-31 20:47:50 线程2：[信息] [信息]  最终决定：显示手动输入弹窗 (进度: 100%)
2025-07-31 20:47:50 线程2：[信息] [信息] 🔴 请手动输入手机号码，然后点击【继续注册】 (进度: 100%)
2025-07-31 20:47:50 线程2：[信息] 已继续
2025-07-31 20:47:50 [信息] 线程2已继续
2025-07-31 20:48:34 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForPhoneInput，当前步骤: 4 (进度: 100%)
2025-07-31 20:48:34 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-07-31 20:48:34 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-31 20:48:34 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-31 20:48:35 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 100%)
2025-07-31 20:48:35 线程1：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-07-31 20:48:35 线程1：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-07-31 20:48:35 线程1：[信息] [信息] 正在从页面读取手机号码... (进度: 100%)
2025-07-31 20:48:35 线程1：[信息] [信息] 从页面读取并保存手机号码: 12568617699（不含区号） (进度: 100%)
2025-07-31 20:48:35 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-07-31 20:48:38 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-07-31 20:48:43 线程1：[信息] [信息] 正在选择月份: June (进度: 100%)
2025-07-31 20:48:43 线程1：[信息] [信息] 已选择月份（标准选项）: June (进度: 100%)
2025-07-31 20:48:44 线程1：[信息] [信息] 正在选择年份: 2027 (进度: 100%)
2025-07-31 20:48:44 线程1：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 100%)
2025-07-31 20:48:45 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-07-31 20:48:45 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-07-31 20:48:45 线程1：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 100%)
2025-07-31 20:48:45 线程1：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 100%)
2025-07-31 20:48:49 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForPhoneInput，当前步骤: 4 (进度: 100%)
2025-07-31 20:48:49 线程2：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-07-31 20:48:49 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-31 20:48:49 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-31 20:48:49 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 100%)
2025-07-31 20:48:49 线程2：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-07-31 20:48:49 线程2：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-07-31 20:48:49 线程2：[信息] [信息] 正在从页面读取手机号码... (进度: 100%)
2025-07-31 20:48:49 线程2：[信息] [信息] 从页面读取并保存手机号码: 522281104534（不含区号） (进度: 100%)
2025-07-31 20:48:49 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-07-31 20:48:50 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-07-31 20:48:51 线程1：[信息] [信息] 已选择国家代码: +1 (进度: 100%)
2025-07-31 20:48:51 线程1：[信息] [信息] 已清空并重新填写手机号码: 12568617699 (进度: 100%)
2025-07-31 20:48:51 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-07-31 20:48:52 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-07-31 20:48:53 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-07-31 20:48:53 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-07-31 20:48:53 线程1：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-07-31 20:48:53 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-07-31 20:48:54 线程2：[信息] [信息] 正在选择月份: October (进度: 100%)
2025-07-31 20:48:54 线程2：[信息] [信息] 已选择月份（标准选项）: October (进度: 100%)
2025-07-31 20:48:55 线程2：[信息] [信息] 正在选择年份: 2027 (进度: 100%)
2025-07-31 20:48:55 线程2：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 100%)
2025-07-31 20:48:56 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-07-31 20:48:56 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-07-31 20:48:56 线程2：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 100%)
2025-07-31 20:48:56 线程2：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 100%)
2025-07-31 20:48:56 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-07-31 20:48:56 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-31 20:49:01 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-07-31 20:49:02 线程2：[信息] [信息] 已选择国家代码: +1 (进度: 100%)
2025-07-31 20:49:02 线程2：[信息] [信息] 已清空并重新填写手机号码: 522281104534 (进度: 100%)
2025-07-31 20:49:02 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-07-31 20:49:03 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35461 字节 (进度: 100%)
2025-07-31 20:49:03 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35461字节，复杂度符合要求 (进度: 100%)
2025-07-31 20:49:03 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-07-31 20:49:04 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-07-31 20:49:04 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-07-31 20:49:04 线程2：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-07-31 20:49:04 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-07-31 20:49:06 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"xc68fc"},"taskId":"be5efa4e-6e0c-11f0-8483-4e7c0aaf4c93"} (进度: 100%)
2025-07-31 20:49:06 线程1：[信息] [信息] 第六页第1次识别结果: xc68fc → 转换为小写: xc68fc (进度: 100%)
2025-07-31 20:49:06 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-07-31 20:49:06 线程1：[信息] [信息] 第六页已填入验证码: xc68fc (进度: 100%)
2025-07-31 20:49:06 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-07-31 20:49:07 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-07-31 20:49:07 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-07-31 20:49:09 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-07-31 20:49:09 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-07-31 20:49:12 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-07-31 20:49:20 线程2：[信息] [信息] 第六页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-07-31 20:49:20 线程2：[信息] [信息] 第六页第1次识别异常: 验证码元素等待超时，需要手动处理 (进度: 100%)
2025-07-31 20:49:20 线程2：[信息] [信息] 第六页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-07-31 20:49:20 线程2：[信息] [信息] 第六页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-07-31 20:49:20 线程2：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-07-31 20:49:20 线程2：[信息] 已继续
2025-07-31 20:49:20 [信息] 线程2已继续
2025-07-31 20:49:25 线程1：[信息] [信息] 第六页处理图形验证码失败: Timeout 10000ms exceeded. (进度: 100%)
2025-07-31 20:49:25 线程1：[信息] [信息] 请手动完成验证码和短信验证码，然后点击【继续注册】 (进度: 100%)
2025-07-31 20:49:25 线程1：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-07-31 20:49:25 线程1：[信息] 已继续
2025-07-31 20:49:25 [信息] 线程1已继续
2025-07-31 20:49:55 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 6 (进度: 100%)
2025-07-31 20:49:55 线程1：[信息] [信息] 第六页手动模式继续注册，检测当前页面状态... (进度: 100%)
2025-07-31 20:49:55 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-31 20:49:55 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-31 20:49:55 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Continue (step 4 of 5)' → 第7页 (进度: 100%)
2025-07-31 20:49:55 线程1：[信息] [信息] ✅ 直接确认为第7页 (进度: 100%)
2025-07-31 20:49:55 线程1：[信息] [信息] 检测到已跳转到第七页，更新步骤并执行第七页逻辑 (进度: 100%)
2025-07-31 20:49:55 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-07-31 20:50:08 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForSMSVerification，当前步骤: 7 (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 🔧 [DEBUG] 未检测到第七页特征 (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Complete sign up' → 第8页 (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] ✅ 直接确认为第8页 (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息]  智能检测到当前在第8页 (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 当前状态: WaitingForSMSVerification, 步骤: 8，尝试智能检测页面... (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Complete sign up' → 第8页 (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] ✅ 直接确认为第8页 (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 智能检测到当前在第8页，继续执行... (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 第七页：智能检测当前页面状态... (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 🔧 [DEBUG] 使用修改后的HandleStep7SmartContinue方法 - 版本2025-07-28 (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 🔧 [DEBUG] 未检测到第七页特征 (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 🔧 [DEBUG] 当前不在第七页，跳过验证码检测 (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 第七页：检测到验证码未输入，根据配置决定自动获取还是手动输入... (进度: 100%)
2025-07-31 20:50:09 线程1：[信息] [信息] 第七页：检测到手机验证码自动模式，开始自动获取验证码流程... (进度: 100%)
2025-07-31 20:50:25 线程1：[信息] [信息] 第七页执行失败: Timeout 30000ms exceeded. (进度: 100%)
2025-07-31 20:50:25 线程1：[信息] 已继续
2025-07-31 20:50:25 [信息] 线程1已继续
2025-07-31 20:50:39 线程1：[信息] [信息] 从页面读取手机号码失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Textbox, new() { Name = "Mobile phone number" }) (进度: 100%)
2025-07-31 20:50:39 线程1：[信息] [信息] 构造完整手机号码: +5212568617699 (本地号码: 12568617699, 区号: +52) (进度: 100%)
2025-07-31 20:50:39 线程1：[信息] [信息] 第七页自动模式：开始自动获取手机验证码... (进度: 100%)
2025-07-31 20:50:39 线程1：[信息] [信息] 第七页自动模式：等待5秒后开始获取验证码... (进度: 100%)
2025-07-31 20:50:44 线程1：[信息] [信息] 线程1开始独立获取验证码... (进度: 100%)
2025-07-31 20:50:44 线程1：[信息] [信息] 线程1美国手机号码信息未初始化 (进度: 100%)
2025-07-31 20:50:44 线程1：[信息] [信息] 美国API验证码获取超时，直接转为手动模式 (进度: 100%)
2025-07-31 20:50:44 线程1：[信息] 已继续
2025-07-31 20:50:44 [信息] 线程1已继续
2025-07-31 20:50:46 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-31 20:50:46 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-07-31 20:50:46 线程1：[信息] 已暂停
2025-07-31 20:50:46 [信息] 线程1已暂停
2025-07-31 20:50:46 [信息] 线程1已暂停
2025-07-31 20:51:31 [信息] 获取线程1当前数据: <EMAIL>
2025-07-31 20:51:31 线程1：[信息] 终止时正在处理的数据: <EMAIL>
2025-07-31 20:51:31 线程1：[信息] 数据详情: <EMAIL>|c9tw0IBR|Anggraini Ria|CrystalGrove Solutions|Tiban Lama Blok A RT 03 RW 12|Riau|Batam|29425|4889506090168356|06|27|555|Anggraini Ria|4I2BP67L|ID
2025-07-31 20:51:31 线程1：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-07-31 20:51:32 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：4I2BP67L ③AWS密码：c9tw0IBR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-07-31 20:51:32 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：4I2BP67L ③AWS密码：c9tw0IBR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:51:32 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：4I2BP67L ③AWS密码：c9tw0IBR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:51:32 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：4I2BP67L ③AWS密码：c9tw0IBR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:51:32 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：4I2BP67L ③AWS密码：c9tw0IBR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:51:32 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：4I2BP67L ③AWS密码：c9tw0IBR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:51:32 线程1：[信息] [信息] 注册已终止 (进度: 100%)
2025-07-31 20:51:32 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-31 20:51:32 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：4I2BP67L ③AWS密码：c9tw0IBR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-07-31 20:51:32 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：4I2BP67L ③AWS密码：c9tw0IBR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:51:32 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：4I2BP67L ③AWS密码：c9tw0IBR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:51:32 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：4I2BP67L ③AWS密码：c9tw0IBR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:51:32 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：4I2BP67L ③AWS密码：c9tw0IBR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:51:32 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：4I2BP67L ③AWS密码：c9tw0IBR ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:51:32 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-07-31 20:51:32 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250731_204120
2025-07-31 20:51:32 线程1：[信息] 已终止
2025-07-31 20:51:32 [信息] 线程1已终止
2025-07-31 20:51:32 [信息] 开始处理线程1终止数据，共1个数据
2025-07-31 20:51:32 [信息] 处理线程1终止数据: <EMAIL>
2025-07-31 20:51:32 [信息] 从注册数据列表中移除: <EMAIL>
2025-07-31 20:51:32 [信息] 线程1终止 - 数据已移动到终止列表: <EMAIL>
2025-07-31 20:51:32 [信息] 线程1终止数据处理完成，成功移动1个数据
2025-07-31 20:51:32 [信息] UniformGrid列数已更新为: 1
2025-07-31 20:51:32 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-07-31 20:51:32 [信息] 线程1已终止
2025-07-31 20:52:25 [信息] 获取线程2当前数据: <EMAIL>
2025-07-31 20:52:25 线程2：[信息] 终止时正在处理的数据: <EMAIL>
2025-07-31 20:52:25 线程2：[信息] 数据详情: <EMAIL>|XJBu027A|Lee Lina|CrimsonEdge Solutions|Komp.Perumahan Jodoh Permai b|Riau|Batam|29433|4889508067025940|10|27|277|Lee Lina|N12drz2I|ID
2025-07-31 20:52:25 线程2：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-07-31 20:52:25 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：N12drz2I ③AWS密码：XJBu027A ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-07-31 20:52:25 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：N12drz2I ③AWS密码：XJBu027A ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:52:25 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：N12drz2I ③AWS密码：XJBu027A ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:52:25 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：N12drz2I ③AWS密码：XJBu027A ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:52:25 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：N12drz2I ③AWS密码：XJBu027A ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:52:25 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：N12drz2I ③AWS密码：XJBu027A ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:52:25 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-31 20:52:25 [信息] 多线程状态已重置
2025-07-31 20:52:25 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-31 20:52:25 [信息] 多线程状态已重置
2025-07-31 20:52:25 线程2：[信息] [信息] 注册已终止 (进度: 100%)
2025-07-31 20:52:25 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-31 20:52:25 [信息] 多线程状态已重置
2025-07-31 20:52:25 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-07-31 20:52:27 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-31 20:52:27 [信息] 多线程状态已重置
2025-07-31 20:52:27 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：N12drz2I ③AWS密码：XJBu027A ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-07-31 20:52:27 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：N12drz2I ③AWS密码：XJBu027A ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:52:27 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：N12drz2I ③AWS密码：XJBu027A ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:52:27 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：N12drz2I ③AWS密码：XJBu027A ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:52:27 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：N12drz2I ③AWS密码：XJBu027A ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:52:27 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：N12drz2I ③AWS密码：XJBu027A ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-07-31 20:52:27 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-31 20:52:27 [信息] 多线程状态已重置
2025-07-31 20:52:27 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-07-31 20:52:27 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250731_204120
2025-07-31 20:52:27 [信息] 所有线程已完成，通知主窗口重置状态
2025-07-31 20:52:27 [信息] 多线程状态已重置
2025-07-31 20:52:27 线程2：[信息] 已终止
2025-07-31 20:52:27 [信息] 线程2已终止
2025-07-31 20:52:27 [信息] 开始处理线程2终止数据，共1个数据
2025-07-31 20:52:27 [信息] 处理线程2终止数据: <EMAIL>
2025-07-31 20:52:27 [信息] 从注册数据列表中移除: <EMAIL>
2025-07-31 20:52:27 [信息] 线程2终止 - 数据已移动到终止列表: <EMAIL>
2025-07-31 20:52:27 [信息] 线程2终止数据处理完成，成功移动1个数据
2025-07-31 20:52:27 [信息] UniformGrid列数已更新为: 1
2025-07-31 20:52:27 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-07-31 20:52:27 [信息] 线程2已终止
2025-07-31 20:52:28 [信息] 多线程窗口引用已清理
2025-07-31 20:52:28 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-07-31 20:52:28 [信息] 多线程管理窗口正在关闭
2025-07-31 22:45:48 [信息] AWS自动注册工具启动
2025-07-31 22:45:48 [信息] 程序版本: 1.0.0.0
2025-07-31 22:45:48 [信息] 启动时间: 2025-07-31 22:45:48
2025-07-31 22:45:48 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-07-31 22:45:48 [信息] 线程数量已选择: 1
2025-07-31 22:45:48 [信息] 线程数量选择初始化完成
2025-07-31 22:45:48 [信息] 程序初始化完成
