2025-08-01 11:37:11 [信息] AWS自动注册工具启动
2025-08-01 11:37:11 [信息] 程序版本: 1.0.0.0
2025-08-01 11:37:11 [信息] 启动时间: 2025-08-01 11:37:11
2025-08-01 11:37:11 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-01 11:37:11 [信息] 线程数量已选择: 1
2025-08-01 11:37:11 [信息] 线程数量选择初始化完成
2025-08-01 11:37:11 [信息] 程序初始化完成
2025-08-01 11:37:13 [按钮操作] 测试浏览器 -> 测试浏览器启动 - 模式: LocalChromeIncognito
2025-08-01 11:37:13 [系统状态] 正在检测Chrome浏览器(无痕模式)...
2025-08-01 11:37:14 [系统状态] 使用默认浏览器语言: English (United States)
2025-08-01 11:37:14 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 11:37:14 [信息] 单线程模式窗口布局: 使用固定位置(0, 0), 大小(600x400)
2025-08-01 11:37:14 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 11:37:14 [系统状态] 尝试启动系统Chrome(无痕模式)...
2025-08-01 11:37:15 [系统状态] 获取无痕模式页面...
2025-08-01 11:37:18 [系统状态] 创建新的无痕模式页面
2025-08-01 11:37:18 [系统状态] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用
2025-08-01 11:37:18 [信息] 无痕Chrome浏览器测试成功
2025-08-01 11:37:25 [信息] 程序正在退出，开始清理工作...
2025-08-01 11:37:25 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-01 11:37:25 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-01 11:37:25 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-01 11:37:25 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-01 11:37:25 [信息] 程序退出清理工作完成
2025-08-01 11:54:36 [信息] AWS自动注册工具启动
2025-08-01 11:54:36 [信息] 程序版本: 1.0.0.0
2025-08-01 11:54:36 [信息] 启动时间: 2025-08-01 11:54:36
2025-08-01 11:54:36 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-01 11:54:36 [信息] 线程数量已选择: 1
2025-08-01 11:54:36 [信息] 线程数量选择初始化完成
2025-08-01 11:54:36 [信息] 程序初始化完成
2025-08-01 11:54:38 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-01 11:54:39 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 11:54:40 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 11:54:40 [信息] 成功加载 12 条数据
2025-08-01 11:54:42 [信息] 线程数量已选择: 3
2025-08-01 11:54:45 [按钮操作] 开始注册 -> 启动注册流程
2025-08-01 11:54:45 [信息] 开始启动多线程注册，线程数量: 3
2025-08-01 11:54:45 [信息] 开始启动多线程注册，线程数量: 3，数据条数: 12
2025-08-01 11:54:45 [信息] 所有线程已停止并清理
2025-08-01 11:54:45 [信息] 正在初始化多线程服务...
2025-08-01 11:54:45 [信息] 美国手机API服务已初始化
2025-08-01 11:54:45 [信息] 手机号码管理器已初始化，服务商: USA，将在第一个线程完成第二页后获取手机号码
2025-08-01 11:54:45 [信息] 多线程服务初始化完成
2025-08-01 11:54:45 [信息] 数据分配完成：共12条数据分配给3个线程
2025-08-01 11:54:45 [信息] 线程1分配到4条数据
2025-08-01 11:54:45 [信息] 线程2分配到4条数据
2025-08-01 11:54:45 [信息] 线程3分配到4条数据
2025-08-01 11:54:45 [信息] 屏幕工作区域: 1280x672
2025-08-01 11:54:45 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x200), 列1行1, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 11:54:45 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-01 11:54:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 [信息] 线程1已创建，窗口位置: (0, 0)
2025-08-01 11:54:45 [信息] 屏幕工作区域: 1280x672
2025-08-01 11:54:45 [信息] 线程2窗口布局: 位置(0, 219), 大小(384x200), 列1行2, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 11:54:45 线程2：[信息] 已创建，窗口位置: (0, 219)
2025-08-01 11:54:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 [信息] 线程2已创建，窗口位置: (0, 219)
2025-08-01 11:54:45 [信息] 屏幕工作区域: 1280x672
2025-08-01 11:54:45 [信息] 线程3窗口布局: 位置(0, 438), 大小(384x200), 列1行3, 宽度30%, 当前列窗口数:3, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 11:54:45 线程3：[信息] 已创建，窗口位置: (0, 438)
2025-08-01 11:54:45 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 线程3：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:54:45 [信息] 线程3已创建，窗口位置: (0, 438)
2025-08-01 11:54:45 [信息] 多线程注册启动成功，共3个线程
2025-08-01 11:54:45 线程1：[信息] 开始启动注册流程
2025-08-01 11:54:45 线程2：[信息] 开始启动注册流程
2025-08-01 11:54:45 线程3：[信息] 开始启动注册流程
2025-08-01 11:54:45 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-01 11:54:45 线程2：[信息] 开始启动浏览器: 位置(0, 219), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 11:54:45 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-01 11:54:45 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-01 11:54:45 线程3：[信息] 开始启动浏览器: 位置(0, 438), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 11:54:45 线程3：[信息] 启动无痕Chrome浏览器...
2025-08-01 11:54:45 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 11:54:45 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 11:54:45 线程3：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 11:54:45 [信息] 多线程管理窗口已初始化
2025-08-01 11:54:45 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:54:45 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-01 11:54:45 [信息] 多线程管理窗口已打开
2025-08-01 11:54:45 [信息] 多线程注册启动成功，共3个线程
2025-08-01 11:54:49 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:54:49 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 11:54:49 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 11:54:49 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 11:54:49 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-01 11:54:49 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 11:54:50 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:54:50 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-01 11:54:50 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 11:54:50 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 11:54:50 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 11:54:50 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 11:54:50 [信息] UniformGrid列数已更新为: 2
2025-08-01 11:54:50 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 3, 列数: 2
2025-08-01 11:54:50 线程3：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 11:54:50 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 11:54:50 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-01 11:54:50 线程3：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 11:54:51 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 11:54:51 线程3：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 11:54:51 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 11:54:54 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 11:54:55 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-08-01 11:54:55 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 11:54:55 线程2：[信息] 浏览器启动成功
2025-08-01 11:54:55 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-01 11:54:55 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-01 11:54:55 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 11:54:55 线程3：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 11:54:55 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 11:54:55 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 11:54:55 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 11:54:55 线程2：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 11:54:55 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-01 11:54:55 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 11:54:55 线程2：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 11:54:55 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 11:54:55 线程2：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 11:54:55 线程2：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 11:54:55 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 已设置浏览器标题: 线程3 - AWS注册工具 (进度: 0%)
2025-08-01 11:54:56 线程3：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 11:54:56 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-08-01 11:54:56 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 11:54:56 线程1：[信息] 浏览器启动成功
2025-08-01 11:54:56 线程3：[信息] 浏览器启动成功
2025-08-01 11:54:56 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-01 11:54:56 线程3：[信息] 获取下一个数据: <EMAIL>
2025-08-01 11:54:56 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-01 11:54:56 线程3：[信息] 开始处理账户: <EMAIL>
2025-08-01 11:54:56 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 11:54:56 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 11:54:56 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 11:54:56 线程1：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 11:54:56 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-01 11:54:56 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0
2025-08-01 11:54:56 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 11:54:56 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 11:54:56 线程3：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 11:54:56 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 11:54:56 线程1：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 11:54:56 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 11:54:56 线程3：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 11:54:56 线程3：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 11:54:56 线程1：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-01 11:54:56 线程1：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 11:54:56 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 11:54:57 线程2：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-01 11:54:57 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-01 11:54:57 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 11:54:57 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 11:54:57 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 11:54:57 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 11:54:57 线程1：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-01 11:54:57 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-01 11:54:57 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 11:54:57 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 11:54:57 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0... (进度: 98%)
2025-08-01 11:54:57 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 11:54:58 线程3：[信息] [信息] 已设置页面视口大小为384x200 (进度: 98%)
2025-08-01 11:54:58 线程3：[信息] [信息] 已重新设置浏览器标题: 线程3 - AWS注册工具 (进度: 98%)
2025-08-01 11:54:58 线程3：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 11:54:58 线程3：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 11:54:58 线程3：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0... (进度: 98%)
2025-08-01 11:54:58 线程3：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 11:55:11 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-01 11:55:11 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 11:55:11 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 11:55:11 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 11:55:11 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 11:55:11 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 11:55:14 线程3：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程3 - AWS注册 (进度: 98%)
2025-08-01 11:55:14 线程3：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 11:55:14 线程3：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 11:55:14 线程3：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 11:55:14 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 11:55:14 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-01 11:55:14 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 11:55:14 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 11:55:14 线程3：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 11:55:14 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 11:55:14 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 11:55:14 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 11:55:14 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 11:55:14 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:55:14 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 11:55:17 线程3：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 11:55:17 线程3：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 11:55:17 线程3：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 11:55:17 线程3：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 11:55:17 线程3：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:55:17 线程3：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-01 11:55:17 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 11:55:17 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 11:55:17 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 11:55:17 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 11:55:18 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:55:18 线程1：[信息] [信息] ⏳ 第1次未发现验证码，等待2秒后继续检测... (进度: 100%)
2025-08-01 11:55:18 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 1781 字节 (进度: 100%)
2025-08-01 11:55:18 线程2：[信息] [信息] ❌ 图片宽度 67px 不符合验证码特征（应为100-300px） (进度: 100%)
2025-08-01 11:55:18 线程2：[信息] [信息] 第一页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] 🔍 第2次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] ✅ 第2次检测发现图形验证码！ (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 11:55:19 线程3：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 🔍 第2次检测图形验证码... (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 🔍 第2次检测 - Security对话框: 0个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 未发现验证码 (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] ✅ 第一页未检测到图形验证码，继续流程 (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 11:55:20 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 11:55:20 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 11:55:20 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 11:55:20 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-01 11:55:20 线程2：[信息] [信息] 第一页图片验证失败，第1次图片不符合验证码特征 (进度: 100%)
2025-08-01 11:55:20 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:55:22 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 11:55:22 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:55:22
2025-08-01 11:55:23 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 1781 字节 (进度: 100%)
2025-08-01 11:55:23 线程2：[信息] [信息] ❌ 图片宽度 67px 不符合验证码特征（应为100-300px） (进度: 100%)
2025-08-01 11:55:23 线程2：[信息] [信息] 第一页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-08-01 11:55:25 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 11:55:25 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:55:25
2025-08-01 11:55:25 线程2：[信息] [信息] 第一页图片验证失败，第2次图片不符合验证码特征 (进度: 100%)
2025-08-01 11:55:25 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:55:28 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 11:55:28 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:55:28
2025-08-01 11:55:28 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 1781 字节 (进度: 100%)
2025-08-01 11:55:28 线程2：[信息] [信息] ❌ 图片宽度 67px 不符合验证码特征（应为100-300px） (进度: 100%)
2025-08-01 11:55:28 线程2：[信息] [信息] 第一页捕获的图片不符合验证码特征，跳过此次尝试（不计入重试次数） (进度: 100%)
2025-08-01 11:55:30 线程2：[信息] [信息] 第一页图片验证失败，第3次图片不符合验证码特征 (进度: 100%)
2025-08-01 11:55:30 线程2：[信息] [信息] 第一页连续3次图片不符合验证码特征，转为手动模式 (进度: 100%)
2025-08-01 11:55:30 线程2：[信息] [信息] 第一页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-08-01 11:55:31 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-01 11:55:31 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:55:31
2025-08-01 11:55:32 线程1：[信息] [信息] 所有自动线程已停止 (进度: 5%)
2025-08-01 11:55:32 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 5%)
2025-08-01 11:55:32 线程1：[信息] 已暂停
2025-08-01 11:55:32 [信息] 线程1已暂停
2025-08-01 11:55:32 [信息] 线程1已暂停
2025-08-01 11:55:32 线程3：[信息] [信息] 第一页验证码元素等待超时，转为手动模式 (进度: 100%)
2025-08-01 11:55:32 线程3：[信息] [信息] 第一页验证码元素等待超时，直接转为手动模式 (进度: 100%)
2025-08-01 11:55:32 线程3：[信息] [信息] 第一页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-08-01 11:55:34 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-08-01 11:55:34 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:55:34
2025-08-01 11:55:36 [信息] 多线程窗口引用已清理
2025-08-01 11:55:36 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-01 11:55:36 [信息] 多线程管理窗口正在关闭
2025-08-01 11:55:37 [信息] 程序正在退出，开始清理工作...
2025-08-01 11:55:37 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-01 11:55:37 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-01 11:55:37 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-01 11:55:37 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-01 11:55:37 [信息] 程序退出清理工作完成
2025-08-01 12:01:05 [信息] AWS自动注册工具启动
2025-08-01 12:01:05 [信息] 程序版本: 1.0.0.0
2025-08-01 12:01:05 [信息] 启动时间: 2025-08-01 12:01:05
2025-08-01 12:01:05 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-01 12:01:05 [信息] 线程数量已选择: 1
2025-08-01 12:01:05 [信息] 线程数量选择初始化完成
2025-08-01 12:01:05 [信息] 程序初始化完成
2025-08-01 12:02:32 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-01 12:02:33 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 12:02:34 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 12:02:34 [信息] 成功加载 12 条数据
2025-08-01 12:02:35 [信息] 线程数量已选择: 2
2025-08-01 12:02:39 [按钮操作] 开始注册 -> 启动注册流程
2025-08-01 12:02:39 [信息] 开始启动多线程注册，线程数量: 2
2025-08-01 12:02:39 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 12
2025-08-01 12:02:39 [信息] 所有线程已停止并清理
2025-08-01 12:02:39 [信息] 正在初始化多线程服务...
2025-08-01 12:02:39 [信息] 美国手机API服务已初始化
2025-08-01 12:02:39 [信息] 手机号码管理器已初始化，服务商: USA，将在第一个线程完成第二页后获取手机号码
2025-08-01 12:02:39 [信息] 多线程服务初始化完成
2025-08-01 12:02:39 [信息] 数据分配完成：共12条数据分配给2个线程
2025-08-01 12:02:39 [信息] 线程1分配到6条数据
2025-08-01 12:02:39 [信息] 线程2分配到6条数据
2025-08-01 12:02:39 [信息] 屏幕工作区域: 1280x672
2025-08-01 12:02:39 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 12:02:39 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-01 12:02:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 [信息] 线程1已创建，窗口位置: (0, 0)
2025-08-01 12:02:39 [信息] 屏幕工作区域: 1280x672
2025-08-01 12:02:39 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 12:02:39 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-01 12:02:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:02:39 [信息] 线程2已创建，窗口位置: (0, 329)
2025-08-01 12:02:39 [信息] 多线程注册启动成功，共2个线程
2025-08-01 12:02:39 线程1：[信息] 开始启动注册流程
2025-08-01 12:02:39 线程2：[信息] 开始启动注册流程
2025-08-01 12:02:39 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 12:02:39 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 12:02:39 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-01 12:02:39 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-01 12:02:39 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 12:02:39 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 12:02:39 [信息] 多线程管理窗口已初始化
2025-08-01 12:02:39 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:02:39 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-01 12:02:39 [信息] 多线程管理窗口已打开
2025-08-01 12:02:39 [信息] 多线程注册启动成功，共2个线程
2025-08-01 12:02:41 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:02:41 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 12:02:41 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 12:02:41 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 12:02:41 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36
2025-08-01 12:02:41 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 12:02:41 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:02:41 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-01 12:02:41 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 12:02:41 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 12:02:41 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-01 12:02:41 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 12:02:42 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 12:02:43 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 12:02:46 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 12:02:48 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 12:02:48 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-08-01 12:02:48 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 12:02:48 线程2：[信息] 浏览器启动成功
2025-08-01 12:02:48 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-01 12:02:48 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-01 12:02:48 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 12:02:48 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 12:02:48 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 12:02:48 线程2：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 12:02:48 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-08-01 12:02:48 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 12:02:48 线程1：[信息] 浏览器启动成功
2025-08-01 12:02:48 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-01 12:02:48 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-01 12:02:48 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 12:02:48 线程2：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 12:02:48 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 12:02:48 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-01 12:02:48 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 12:02:48 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 12:02:48 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 12:02:48 线程1：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 12:02:48 线程2：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:02:49 线程2：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 12:02:49 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 12:02:49 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 12:02:49 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 12:02:49 线程1：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 12:02:49 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 12:02:49 线程1：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:02:49 线程1：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 12:02:49 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 12:02:50 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 12:02:50 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-01 12:02:50 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 12:02:50 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 12:02:50 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:02:50 线程2：[信息] [信息] 正在新建标签页打开AWS注册页面... (进度: 98%)
2025-08-01 12:02:51 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 12:02:51 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-01 12:02:51 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 12:02:52 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 12:02:52 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:02:52 线程1：[信息] [信息] 正在新建标签页打开AWS注册页面... (进度: 98%)
2025-08-01 12:03:04 线程2：[信息] [信息] AWS注册页面已在新标签页中打开，测试页面保持显示线程号 (进度: 98%)
2025-08-01 12:03:04 线程1：[信息] [信息] AWS注册页面已在新标签页中打开，测试页面保持显示线程号 (进度: 98%)
2025-08-01 12:03:06 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-01 12:03:06 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 12:03:06 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 12:03:06 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 12:03:06 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 12:03:09 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-01 12:03:09 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 12:03:09 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 12:03:09 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 12:03:09 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 12:03:10 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 12:03:10 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 12:03:12 [信息] 多线程窗口引用已清理
2025-08-01 12:03:12 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-01 12:03:12 [信息] 多线程管理窗口正在关闭
2025-08-01 12:03:13 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 12:03:13 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 12:03:13 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 12:03:13 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] 检查IP异常错误时出现异常: Target page, context or browser has been closed (进度: 98%)
2025-08-01 12:03:13 [信息] 检查IP异常错误时出现异常: Target page, context or browser has been closed
2025-08-01 12:03:13 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] ❌ 第一页图形验证码处理异常: Target page, context or browser has been closed (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] ❌ 异常详情:    at Microsoft.Playwright.Transport.Connection.InnerSendMessageToServerAsync[T](ChannelOwnerBase object, String method, Dictionary`2 dictionary, Boolean keepNulls) in /_/src/Playwright/Transport/Connection.cs:line 209
   at Microsoft.Playwright.Transport.Connection.WrapApiCallAsync[T](Func`1 action, Boolean isInternal) in /_/src/Playwright/Transport/Connection.cs:line 535
   at Microsoft.Playwright.Transport.Channels.FrameChannel.QueryCountAsync(String selector) in /_/src/Playwright/Transport/Channels/FrameChannel.cs:line 262
   at AWSAutoRegister.Services.AutomationService.CheckForCaptchaOnPage1() in C:\Users\<USER>\Desktop\AGaws单线程版-备份\Services\AutomationService.cs:line 12013 (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] [信息] 等待验证码页面超时 (进度: 98%)
2025-08-01 12:03:13 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 12:03:13 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 12:03:14 [信息] 程序正在退出，开始清理工作...
2025-08-01 12:03:14 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-01 12:03:14 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-01 12:03:14 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-01 12:03:14 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-01 12:03:14 [信息] 程序退出清理工作完成
2025-08-01 12:10:58 [信息] AWS自动注册工具启动
2025-08-01 12:10:58 [信息] 程序版本: 1.0.0.0
2025-08-01 12:10:58 [信息] 启动时间: 2025-08-01 12:10:58
2025-08-01 12:10:58 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-01 12:10:58 [信息] 线程数量已选择: 1
2025-08-01 12:10:58 [信息] 线程数量选择初始化完成
2025-08-01 12:10:58 [信息] 程序初始化完成
2025-08-01 12:11:03 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-01 12:11:05 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 12:11:05 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 12:11:06 [信息] 成功加载 12 条数据
2025-08-01 12:11:08 [信息] 线程数量已选择: 2
2025-08-01 12:11:12 [按钮操作] 开始注册 -> 启动注册流程
2025-08-01 12:11:12 [信息] 开始启动多线程注册，线程数量: 2
2025-08-01 12:11:12 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 12
2025-08-01 12:11:12 [信息] 所有线程已停止并清理
2025-08-01 12:11:12 [信息] 正在初始化多线程服务...
2025-08-01 12:11:12 [信息] 美国手机API服务已初始化
2025-08-01 12:11:12 [信息] 手机号码管理器已初始化，服务商: USA，将在第一个线程完成第二页后获取手机号码
2025-08-01 12:11:12 [信息] 多线程服务初始化完成
2025-08-01 12:11:12 [信息] 数据分配完成：共12条数据分配给2个线程
2025-08-01 12:11:12 [信息] 线程1分配到6条数据
2025-08-01 12:11:12 [信息] 线程2分配到6条数据
2025-08-01 12:11:12 [信息] 屏幕工作区域: 1280x672
2025-08-01 12:11:12 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 12:11:12 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-01 12:11:12 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 [信息] 线程1已创建，窗口位置: (0, 0)
2025-08-01 12:11:12 [信息] 屏幕工作区域: 1280x672
2025-08-01 12:11:12 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 12:11:12 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-01 12:11:12 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 12:11:12 [信息] 线程2已创建，窗口位置: (0, 329)
2025-08-01 12:11:12 [信息] 多线程注册启动成功，共2个线程
2025-08-01 12:11:12 线程2：[信息] 开始启动注册流程
2025-08-01 12:11:12 线程1：[信息] 开始启动注册流程
2025-08-01 12:11:12 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 12:11:12 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-01 12:11:12 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-01 12:11:12 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-01 12:11:12 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 12:11:12 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 12:11:12 [信息] 多线程管理窗口已初始化
2025-08-01 12:11:12 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:11:12 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-01 12:11:12 [信息] 多线程管理窗口已打开
2025-08-01 12:11:12 [信息] 多线程注册启动成功，共2个线程
2025-08-01 12:11:14 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:11:14 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 12:11:14 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 12:11:14 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 12:11:14 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36 Edg/119.0.0.0
2025-08-01 12:11:14 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 12:11:14 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:11:14 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-01 12:11:14 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 12:11:14 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 12:11:14 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
2025-08-01 12:11:14 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 12:11:15 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 12:11:15 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 12:11:22 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 12:11:22 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 12:11:23 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-08-01 12:11:23 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 12:11:23 线程2：[信息] 浏览器启动成功
2025-08-01 12:11:23 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-01 12:11:23 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-01 12:11:23 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 12:11:23 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 12:11:23 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 12:11:23 线程2：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 12:11:23 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-08-01 12:11:23 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 12:11:23 线程1：[信息] 浏览器启动成功
2025-08-01 12:11:23 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-01 12:11:23 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36
2025-08-01 12:11:23 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 12:11:23 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-01 12:11:23 线程2：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 12:11:23 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 12:11:23 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 12:11:23 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 12:11:23 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 12:11:23 线程1：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 12:11:23 线程2：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:11:23 线程2：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 12:11:23 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 12:11:23 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-01 12:11:23 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 12:11:23 线程1：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 12:11:23 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 12:11:23 线程1：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:11:23 线程1：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 12:11:23 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 12:11:24 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 12:11:24 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-01 12:11:24 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 12:11:24 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 12:11:24 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:11:24 线程2：[信息] [信息] 正在新建标签页打开AWS注册页面... (进度: 98%)
2025-08-01 12:11:25 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 12:11:25 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-01 12:11:25 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 12:11:25 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 12:11:25 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 12:11:25 线程1：[信息] [信息] 正在新建标签页打开AWS注册页面... (进度: 98%)
2025-08-01 12:11:37 线程2：[信息] [信息] AWS注册页面已在新标签页中打开，测试页面保持显示线程号 (进度: 98%)
2025-08-01 12:11:37 线程1：[信息] [信息] AWS注册页面已在新标签页中打开，测试页面保持显示线程号 (进度: 98%)
2025-08-01 12:11:38 线程2：[信息] [信息] 注册失败: Execution context was destroyed, most likely because of a navigation (进度: 98%)
2025-08-01 12:11:38 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 98%)
2025-08-01 12:11:38 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 线程2：[错误] 账户注册启动失败: <EMAIL>
2025-08-01 12:11:38 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 12:11:38 线程1：[信息] [信息] 注册失败: Execution context was destroyed, most likely because of a navigation (进度: 98%)
2025-08-01 12:11:38 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:11:38 [信息] 多线程状态已重置
2025-08-01 12:11:38 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:11:38 [信息] 多线程状态已重置
2025-08-01 12:11:38 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 98%)
2025-08-01 12:11:38 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:11:38 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:11:38 [信息] 多线程状态已重置
2025-08-01 12:11:38 线程1：[错误] 账户注册启动失败: <EMAIL>
2025-08-01 12:11:38 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 12:12:12 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:12 [信息] 多线程状态已重置
2025-08-01 12:12:12 线程1：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-01 12:12:12 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:12 [信息] 多线程状态已重置
2025-08-01 12:12:12 线程1：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-01 12:12:12 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:12 [信息] 多线程状态已重置
2025-08-01 12:12:12 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-01 12:12:12 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:12 [信息] 多线程状态已重置
2025-08-01 12:12:12 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程2：[信息] [信息]  继续注册被调用，当前状态: Error，当前步骤: 1 (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程2：[信息] [信息]  进行智能页面检测... (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 0%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-01 12:12:13 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:13 [信息] 多线程状态已重置
2025-08-01 12:12:13 线程1：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-01 12:12:14 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:12:14 [信息] 多线程状态已重置
2025-08-01 12:12:14 线程1：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-01 12:12:14 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-01 12:12:14 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-01 12:12:14 线程2：[信息] [信息]  智能检测到当前在第1页 (进度: 100%)
2025-08-01 12:12:14 线程2：[信息] [信息] 检测当前页面状态... (进度: 100%)
2025-08-01 12:12:14 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-01 12:12:14 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-01 12:12:15 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-01 12:12:15 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Verify email address' → 第1页 (进度: 100%)
2025-08-01 12:12:15 线程2：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-01 12:12:15 线程1：[信息] [信息] ✅ 直接确认为第1页 (进度: 100%)
2025-08-01 12:12:15 线程2：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-01 12:12:15 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-01 12:12:15 线程1：[信息] [信息] 智能检测到当前在第1页，继续执行... (进度: 100%)
2025-08-01 12:12:15 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 100%)
2025-08-01 12:12:15 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-01 12:12:15 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 12:12:15 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 100%)
2025-08-01 12:12:15 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 12:12:15 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-01 12:12:15 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 100%)
2025-08-01 12:12:18 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-01 12:12:18 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 12:12:19 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 12:12:19 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 12:12:22 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 29379 字节 (进度: 100%)
2025-08-01 12:12:22 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，29379字节，复杂度符合要求 (进度: 100%)
2025-08-01 12:12:22 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 12:12:23 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 29664 字节 (进度: 100%)
2025-08-01 12:12:23 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，29664字节，复杂度符合要求 (进度: 100%)
2025-08-01 12:12:23 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 12:12:24 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"6ggsgf"},"taskId":"b9f6e3d2-6e8d-11f0-9ac6-5ee226f458ac"} (进度: 100%)
2025-08-01 12:12:24 线程2：[信息] [信息] 第一页第1次识别结果: 6ggsgf → 转换为小写: 6ggsgf (进度: 100%)
2025-08-01 12:12:24 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 12:12:24 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"d3ctp4"},"taskId":"ba004cb0-6e8d-11f0-af44-ba19b662e7c3"} (进度: 100%)
2025-08-01 12:12:24 线程1：[信息] [信息] 第一页第1次识别结果: d3ctp4 → 转换为小写: d3ctp4 (进度: 100%)
2025-08-01 12:12:24 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 12:12:24 线程2：[信息] [信息] 已填入验证码: 6ggsgf (进度: 100%)
2025-08-01 12:12:24 线程1：[信息] [信息] 已填入验证码: d3ctp4 (进度: 100%)
2025-08-01 12:12:24 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 12:12:24 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 12:12:26 线程2：[信息] 已继续
2025-08-01 12:12:26 [信息] 线程2已继续
2025-08-01 12:12:26 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 12:12:26 线程1：[信息] 已继续
2025-08-01 12:12:26 [信息] 线程1已继续
2025-08-01 12:12:26 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 12:12:26 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 12:12:26 [信息] [线程1] 已删除旧的请求文件
2025-08-01 12:12:26 [信息] [线程2] 已删除旧的响应文件
2025-08-01 12:12:26 [信息] [线程1] 已删除旧的响应文件
2025-08-01 12:12:26 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-01 12:12:26 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-01 12:12:28 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 12:12:28 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 12:12:28
2025-08-01 12:12:28 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 12:12:28 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 12:12:28
2025-08-01 12:12:32 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 12:12:32 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 12:12:32 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 12:12:32
2025-08-01 12:12:32 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 12:12:32
2025-08-01 12:12:34 [信息] [线程2] 邮箱验证码获取成功: 020526，立即停止重复请求
2025-08-01 12:12:34 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-01 12:12:34 [信息] [线程2] 已清理响应文件
2025-08-01 12:12:34 线程2：[信息] [信息] 验证码获取成功: 020526，正在自动填入... (进度: 100%)
2025-08-01 12:12:34 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-01 12:12:34 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-01 12:12:34 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-01 12:12:34 [信息] 线程2完成第二页事件已处理
2025-08-01 12:12:34 [信息] 线程2完成第二页，开始批量获取手机号码...
2025-08-01 12:12:34 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 100%)
2025-08-01 12:12:34 [信息] 开始批量获取2个手机号码，服务商: USA
2025-08-01 12:12:34 [信息] 美国API开始批量获取2个手机号码
2025-08-01 12:12:34 [信息] [美国API] 开始批量购买2个手机号码
2025-08-01 12:12:34 [信息] [美国API] 请求购买2个手机号码: https://www.api21k.com/api/v1/buy/create
2025-08-01 12:12:35 [信息] [线程1] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 12:12:35 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 12:12:35
2025-08-01 12:12:36 [信息] [美国API] 购买响应: {"code":1,"msg":"购买成功","time":1754021554,"data":{"ordernum":"25080112123567660","api_count":2}}
2025-08-01 12:12:36 [信息] [美国API] 购买成功，订单号: 25080112123567660，数量: 2
2025-08-01 12:12:36 [信息] [美国API] 批量购买成功，订单号: 25080112123567660
2025-08-01 12:12:36 [信息] [美国API] 请求获取手机号码列表: https://www.api21k.com/api/v1/order/api
2025-08-01 12:12:36 [信息] [美国API] 获取列表响应: {"code":1,"msg":"获取订单API列表成功","time":1754021555,"data":{"url_list":["https:\/\/api.sms8.net","https:\/\/api.smsapi.cc","http:\/\/47.76.194.115"],"list":[{"id":56662674,"app_id":81,"cate_id":2,"type":1,"tel":"17159522238","token":"a71gmleupt9dhrw8wnsx6hqv5dod8f0591yj","end_time":"2025-09-30 00:00:00","sms_count":0,"voice_count":0,"remark":"","status":0,"api":"https:\/\/api.sms8.net\/api\/record?token=a71gmleupt9dhrw8wnsx6hqv5dod8f0591yj"},{"id":56662675,"app_id":81,"cate_id":2,"type":1,"tel":"15308004312","token":"bs341g2151glws9k0bq4pbr8qbsgxm2ep14v","end_time":"2025-09-30 00:00:00","sms_count":0,"voice_count":0,"remark":"","status":0,"api":"https:\/\/api.sms8.net\/api\/record?token=bs341g2151glws9k0bq4pbr8qbsgxm2ep14v"}],"total":2}}
2025-08-01 12:12:36 [信息] [美国API] 解析号码: +17159522238
2025-08-01 12:12:36 [信息] [美国API] 解析号码: +15308004312
2025-08-01 12:12:36 [信息] [美国API] 获取号码列表成功，共2个
2025-08-01 12:12:36 [信息] [美国API] 批量获取手机号码成功，共2个
2025-08-01 12:12:36 [信息] 线程1分配美国手机号码: +17159522238
2025-08-01 12:12:36 [信息] [调试] 美国API字典添加：线程1 -> +17159522238
2025-08-01 12:12:36 [信息] 线程2分配美国手机号码: +15308004312
2025-08-01 12:12:36 [信息] [调试] 美国API字典添加：线程2 -> +15308004312
2025-08-01 12:12:36 [信息] [调试] 美国API分配完成后，字典中的线程数量: 2
2025-08-01 12:12:36 [信息] [调试] 美国API分配完成后，字典中的线程ID: [1, 2]
2025-08-01 12:12:36 [信息] 美国API批量获取手机号码成功，共2个
2025-08-01 12:12:36 [信息] 批量获取2个手机号码成功
2025-08-01 12:12:37 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-01 12:12:37 线程2：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-01 12:12:37 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-01 12:12:37 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-01 12:12:37 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-01 12:12:38 [信息] [线程1] 第4次触发邮箱验证码获取...（最多20次）
2025-08-01 12:12:38 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 12:12:38
2025-08-01 12:12:43 [信息] [线程1] 第5次触发邮箱验证码获取...（最多20次）
2025-08-01 12:12:43 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 12:12:43
2025-08-01 12:12:43 [信息] [线程1] 邮箱验证码获取成功: 927969，立即停止重复请求
2025-08-01 12:12:43 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-01 12:12:43 [信息] [线程1] 已清理响应文件
2025-08-01 12:12:43 线程1：[信息] [信息] 验证码获取成功: 927969，正在自动填入... (进度: 100%)
2025-08-01 12:12:43 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 100%)
2025-08-01 12:12:43 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-01 12:12:43 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 100%)
2025-08-01 12:12:46 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-01 12:12:46 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-01 12:12:46 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-01 12:12:46 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 100%)
2025-08-01 12:12:46 [信息] 线程1完成第二页事件已处理
2025-08-01 12:12:46 [信息] 线程1完成第二页，手机号码已获取，无需重复获取
2025-08-01 12:12:46 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 100%)
2025-08-01 12:12:49 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 100%)
2025-08-01 12:12:49 线程1：[信息] [信息] 开始填写密码信息... (进度: 100%)
2025-08-01 12:12:49 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-01 12:12:49 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 100%)
2025-08-01 12:12:49 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 100%)
2025-08-01 12:12:50 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-01 12:12:50 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-01 12:12:50 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 100%)
2025-08-01 12:12:56 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 100%)
2025-08-01 12:12:56 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 100%)
2025-08-01 12:12:56 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 100%)
2025-08-01 12:12:57 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-01 12:12:58 线程2：[信息] [信息] 数据国家代码为AZ，需要选择Azerbaijan (进度: 100%)
2025-08-01 12:13:00 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-01 12:13:00 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 100%)
2025-08-01 12:13:00 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 100%)
2025-08-01 12:13:02 线程2：[信息] [信息] 已选择国家: Azerbaijan (进度: 100%)
2025-08-01 12:13:02 线程2：[信息] [信息] 已成功选择国家: Azerbaijan (进度: 100%)
2025-08-01 12:13:02 线程2：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 100%)
2025-08-01 12:13:02 线程2：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 100%)
2025-08-01 12:13:02 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-01 12:13:03 线程2：[信息] [信息] 已选择国家代码 +1 (进度: 100%)
2025-08-01 12:13:04 线程2：[信息] [信息] 开始检查手机号码状态... (进度: 100%)
2025-08-01 12:13:04 线程2：[信息] [信息]  检查页面是否已有手机号码填写... (进度: 100%)
2025-08-01 12:13:04 线程2：[信息] [信息]  页面手机号码输入框当前值: '' (进度: 100%)
2025-08-01 12:13:04 线程2：[信息] [信息]  页面手机号码输入框为空，需要手动输入 (进度: 100%)
2025-08-01 12:13:04 线程2：[信息] [信息]  最终决定：显示手动输入弹窗 (进度: 100%)
2025-08-01 12:13:04 线程2：[信息] [信息] 🔴 请手动输入手机号码，然后点击【继续注册】 (进度: 100%)
2025-08-01 12:13:07 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 100%)
2025-08-01 12:13:09 线程1：[信息] [信息] 数据国家代码为AZ，需要选择Azerbaijan (进度: 100%)
2025-08-01 12:13:10 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 100%)
2025-08-01 12:13:11 线程1：[信息] [信息] 已选择国家: Azerbaijan (进度: 100%)
2025-08-01 12:13:11 线程1：[信息] [信息] 已成功选择国家: Azerbaijan (进度: 100%)
2025-08-01 12:13:11 线程1：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 100%)
2025-08-01 12:13:11 线程1：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 100%)
2025-08-01 12:13:12 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-01 12:13:13 线程1：[信息] [信息] 已选择国家代码 +1 (进度: 100%)
2025-08-01 12:13:13 线程1：[信息] [信息] 开始检查手机号码状态... (进度: 100%)
2025-08-01 12:13:13 线程1：[信息] [信息]  检查页面是否已有手机号码填写... (进度: 100%)
2025-08-01 12:13:13 线程1：[信息] [信息]  页面手机号码输入框当前值: '' (进度: 100%)
2025-08-01 12:13:13 线程1：[信息] [信息]  页面手机号码输入框为空，需要手动输入 (进度: 100%)
2025-08-01 12:13:13 线程1：[信息] [信息]  最终决定：显示手动输入弹窗 (进度: 100%)
2025-08-01 12:13:13 线程1：[信息] [信息] 🔴 请手动输入手机号码，然后点击【继续注册】 (进度: 100%)
2025-08-01 12:13:41 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForPhoneInput，当前步骤: 4 (进度: 100%)
2025-08-01 12:13:41 线程1：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-01 12:13:41 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-01 12:13:41 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-01 12:13:41 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForPhoneInput，当前步骤: 4 (进度: 100%)
2025-08-01 12:13:41 线程2：[信息] [信息]  进行智能页面检测... (进度: 100%)
2025-08-01 12:13:41 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 100%)
2025-08-01 12:13:41 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 100%)
2025-08-01 12:13:43 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 100%)
2025-08-01 12:13:43 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 100%)
2025-08-01 12:13:43 线程1：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-01 12:13:43 线程2：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-01 12:13:43 线程1：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-01 12:13:43 线程1：[信息] [信息] 正在从页面读取手机号码... (进度: 100%)
2025-08-01 12:13:43 线程2：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-01 12:13:43 线程2：[信息] [信息] 正在从页面读取手机号码... (进度: 100%)
2025-08-01 12:13:43 线程1：[信息] [信息] 从页面读取并保存手机号码: 17159522238（不含区号） (进度: 100%)
2025-08-01 12:13:43 线程2：[信息] [信息] 从页面读取并保存手机号码: 15308004312（不含区号） (进度: 100%)
2025-08-01 12:13:43 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-01 12:13:43 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-01 12:13:46 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-01 12:13:46 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-01 12:13:47 线程2：[信息] [信息] 正在选择月份: December (进度: 100%)
2025-08-01 12:13:47 线程1：[信息] [信息] 正在选择月份: September (进度: 100%)
2025-08-01 12:13:48 线程2：[信息] [信息] 已选择月份（标准选项）: December (进度: 100%)
2025-08-01 12:13:48 线程1：[信息] [信息] 已选择月份（标准选项）: September (进度: 100%)
2025-08-01 12:13:48 线程2：[信息] [信息] 正在选择年份: 2025 (进度: 100%)
2025-08-01 12:13:49 线程2：[信息] [信息] 已选择年份（标准选项）: 2025 (进度: 100%)
2025-08-01 12:13:49 线程1：[信息] [信息] 正在选择年份: 2025 (进度: 100%)
2025-08-01 12:13:49 线程1：[信息] [信息] 已选择年份（标准选项）: 2025 (进度: 100%)
2025-08-01 12:13:50 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-01 12:13:50 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-01 12:13:50 线程2：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 100%)
2025-08-01 12:13:50 线程2：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 100%)
2025-08-01 12:13:50 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-01 12:13:50 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-01 12:13:50 线程1：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 100%)
2025-08-01 12:13:50 线程1：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 100%)
2025-08-01 12:13:54 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-01 12:13:54 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-01 12:13:55 线程2：[信息] [信息] 已选择国家代码: +1 (进度: 100%)
2025-08-01 12:13:55 线程2：[信息] [信息] 已清空并重新填写手机号码: 15308004312 (进度: 100%)
2025-08-01 12:13:55 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-01 12:13:55 线程1：[信息] [信息] 已选择国家代码: +1 (进度: 100%)
2025-08-01 12:13:55 线程1：[信息] [信息] 已清空并重新填写手机号码: 17159522238 (进度: 100%)
2025-08-01 12:13:56 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-01 12:13:58 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-01 12:13:58 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-01 12:13:58 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-01 12:13:58 线程2：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-01 12:13:58 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-01 12:13:59 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-01 12:13:59 线程1：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-01 12:13:59 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-01 12:14:01 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-01 12:14:01 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 12:14:02 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-01 12:14:02 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 12:14:05 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 29974 字节 (进度: 100%)
2025-08-01 12:14:05 线程2：[信息] [信息] ✅ 图片验证通过：201x70px，29974字节，复杂度符合要求 (进度: 100%)
2025-08-01 12:14:05 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 12:14:05 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 30070 字节 (进度: 100%)
2025-08-01 12:14:05 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，30070字节，复杂度符合要求 (进度: 100%)
2025-08-01 12:14:05 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 12:14:05 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"6cxgtz"},"taskId":"f65abf10-6e8d-11f0-856b-faec61c6fa30"} (进度: 100%)
2025-08-01 12:14:05 线程2：[信息] [信息] 第六页第1次识别结果: 6cxgtz → 转换为小写: 6cxgtz (进度: 100%)
2025-08-01 12:14:05 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 12:14:05 线程2：[信息] [信息] 第六页已填入验证码: 6cxgtz (进度: 100%)
2025-08-01 12:14:06 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 12:14:06 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"afnsn"},"taskId":"f6a33d8a-6e8d-11f0-83b6-faec61c6fa30"} (进度: 100%)
2025-08-01 12:14:06 线程1：[信息] [信息] 第六页第1次识别结果: afnsn → 转换为小写: afnsn (进度: 100%)
2025-08-01 12:14:06 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 12:14:06 线程1：[信息] [信息] 第六页已填入验证码: afnsn (进度: 100%)
2025-08-01 12:14:06 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 12:14:09 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-01 12:14:09 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-01 12:14:10 线程1：[信息] [信息] 第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-01 12:14:10 线程1：[信息] [信息] 第六页第1次识别异常: 验证码错误 (进度: 100%)
2025-08-01 12:14:12 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-01 12:14:12 线程1：[信息] [信息] 第六页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 12:14:15 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-01 12:14:15 线程2：[信息] [信息] 第七页手动模式：请手动输入验证码 (进度: 100%)
2025-08-01 12:14:15 线程2：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-01 12:14:23 线程2：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-01 12:14:23 线程2：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-01 12:14:23 线程2：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 7017位 (进度: 100%)
2025-08-01 12:14:23 线程2：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-01 12:14:23 线程2：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-01 12:14:24 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 30065 字节 (进度: 100%)
2025-08-01 12:14:24 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，30065字节，复杂度符合要求 (进度: 100%)
2025-08-01 12:14:24 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 12:14:24 线程2：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-01 12:14:24 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"ymt5et"},"taskId":"01a09f48-6e8e-11f0-a542-d6d3a68b7a1f"} (进度: 100%)
2025-08-01 12:14:24 线程1：[信息] [信息] 第六页第2次识别结果: ymt5et → 转换为小写: ymt5et (进度: 100%)
2025-08-01 12:14:24 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 12:14:24 线程1：[信息] [信息] 第六页已填入验证码: ymt5et (进度: 100%)
2025-08-01 12:14:24 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 12:14:27 线程2：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-01 12:14:27 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-01 12:14:29 线程1：[信息] [信息] 第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-01 12:14:29 线程1：[信息] [信息] 第六页第2次识别异常: 验证码错误 (进度: 100%)
2025-08-01 12:14:31 线程1：[信息] [信息] 第六页第3次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 12:14:34 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 30026 字节 (进度: 100%)
2025-08-01 12:14:34 线程1：[信息] [信息] ✅ 图片验证通过：201x70px，30026字节，复杂度符合要求 (进度: 100%)
2025-08-01 12:14:34 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 12:14:35 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"m3ymtc"},"taskId":"07d22198-6e8e-11f0-8478-5ee226f458ac"} (进度: 100%)
2025-08-01 12:14:35 线程1：[信息] [信息] 第六页第3次识别结果: m3ymtc → 转换为小写: m3ymtc (进度: 100%)
2025-08-01 12:14:35 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 12:14:35 线程1：[信息] [信息] 第六页已填入验证码: m3ymtc (进度: 100%)
2025-08-01 12:14:35 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 12:14:38 [信息] 获取线程2当前数据: <EMAIL>
2025-08-01 12:14:38 线程2：[信息] 终止时正在处理的数据: <EMAIL>
2025-08-01 12:14:38 线程2：[信息] 数据详情: <EMAIL>|4IQkNAMp|Mehriban Karimov|GoldenStream Solutions|4104 Rashid Behbudov Street|Mingachevir|GA|AZ2000|5167513346925043|12|25|200|Mehriban Karimov|V4g5s1dR41ZB|AZ
2025-08-01 12:14:38 线程2：[信息] 多线程终止 - 复制密钥信息到剪贴板
2025-08-01 12:14:38 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-01 12:14:38 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:14:38 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:14:38 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:14:38 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:14:38 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:14:38 线程2：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-01 12:14:38 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-01 12:14:38 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-01 12:14:38 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:14:38 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:14:38 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:14:38 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:14:38 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：V4g5s1dR41ZB ③AWS密码：4IQkNAMp ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:14:38 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-01 12:14:38 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250801_121112
2025-08-01 12:14:38 线程2：[信息] 已终止
2025-08-01 12:14:38 [信息] 线程2已终止
2025-08-01 12:14:38 [信息] 开始处理线程2终止数据，共1个数据
2025-08-01 12:14:38 [信息] 处理线程2终止数据: <EMAIL>
2025-08-01 12:14:38 [信息] 从注册数据列表中移除: <EMAIL>
2025-08-01 12:14:38 [信息] 线程2终止 - 数据已移动到终止列表: <EMAIL>
2025-08-01 12:14:38 [信息] 线程2终止数据处理完成，成功移动1个数据
2025-08-01 12:14:38 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:14:38 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 12:14:38 [信息] 线程2已终止
2025-08-01 12:14:38 线程1：[信息] [信息] 第3次图形验证码识别成功 (进度: 100%)
2025-08-01 12:14:38 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-01 12:14:39 线程2：[信息] [信息] 点击完成注册按钮失败: Target page, context or browser has been closed
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 88%)
2025-08-01 12:14:39 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 88%)
2025-08-01 12:14:41 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-01 12:14:42 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 88%)
2025-08-01 12:14:42 线程2：[信息] [信息] 注册已被终止，停止密钥提取流程 (进度: 98%)
2025-08-01 12:14:42 线程2：[信息] [信息] 注册已被终止 (进度: 98%)
2025-08-01 12:14:42 线程2：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 98%)
2025-08-01 12:14:42 线程2：[信息] 已继续
2025-08-01 12:14:42 [信息] 线程2已继续
2025-08-01 12:14:44 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-01 12:14:44 线程1：[信息] [信息] 第七页手动模式：请手动输入验证码 (进度: 100%)
2025-08-01 12:14:44 线程1：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-01 12:14:44 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-01 12:14:44 线程1：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-01 12:14:44 线程1：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 7017位 (进度: 100%)
2025-08-01 12:14:44 线程1：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-01 12:14:44 线程1：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-01 12:14:44 线程1：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-01 12:14:50 线程1：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-01 12:14:50 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-01 12:14:50 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-01 12:14:51 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-01 12:15:00 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-01 12:15:00 线程1：[信息] [信息] 注册已暂停，所有自动线程已停止 (进度: 100%)
2025-08-01 12:15:00 线程1：[信息] 已暂停
2025-08-01 12:15:00 [信息] 线程1已暂停
2025-08-01 12:15:00 [信息] 线程1已暂停
2025-08-01 12:15:01 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-01 12:15:01 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-01 12:15:06 线程1：[信息] [信息] ❌ 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible (进度: 100%)
2025-08-01 12:15:06 [信息] 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible
2025-08-01 12:15:06 线程1：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-01 12:15:06 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-01 12:15:06 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-01 12:15:06 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:15:06 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:15:06 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:15:06 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:15:06 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 12:15:06 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-01 12:15:06 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:15:06 [信息] 多线程状态已重置
2025-08-01 12:15:06 线程1：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-01 12:15:06 线程1：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-01 12:15:06 [信息] 注册完成（密钥提取失败）
2025-08-01 12:15:06 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-01 12:15:06 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-01 12:15:06 [信息] 已完成数据移除: <EMAIL>
2025-08-01 12:15:06 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-01 12:15:06 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:15:06 [信息] 多线程状态已重置
2025-08-01 12:15:06 线程1：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-01 12:15:06 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:15:06 [信息] 多线程状态已重置
2025-08-01 12:15:06 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-01 12:15:06 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:15:06 [信息] 多线程状态已重置
2025-08-01 12:15:06 线程1：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-01 12:15:06 线程1：[信息] 已继续
2025-08-01 12:15:06 [信息] 线程1已继续
2025-08-01 12:15:06 [信息] 继续了 2 个可继续的线程
2025-08-01 12:15:19 [信息] 获取线程1当前数据: 无
2025-08-01 12:15:19 线程1：[信息] 终止时没有正在处理的数据
2025-08-01 12:15:19 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:15:19 [信息] 多线程状态已重置
2025-08-01 12:15:19 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:15:19 [信息] 多线程状态已重置
2025-08-01 12:15:19 线程1：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-01 12:15:19 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:15:19 [信息] 多线程状态已重置
2025-08-01 12:15:19 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-01 12:15:19 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:15:19 [信息] 多线程状态已重置
2025-08-01 12:15:19 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-01 12:15:19 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:15:19 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:15:19 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:15:19 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:15:19 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：H1S137r5 ③AWS密码：2c8NX8jG ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 12:15:19 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:15:19 [信息] 多线程状态已重置
2025-08-01 12:15:19 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-01 12:15:19 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250801_121112
2025-08-01 12:15:19 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 12:15:19 [信息] 多线程状态已重置
2025-08-01 12:15:19 线程1：[信息] 已终止
2025-08-01 12:15:19 [信息] 线程1已终止
2025-08-01 12:15:19 [信息] UniformGrid列数已更新为: 1
2025-08-01 12:15:19 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-01 12:15:19 [信息] 线程1已终止
2025-08-01 12:15:41 [信息] 多线程窗口引用已清理
2025-08-01 12:15:41 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-01 12:15:41 [信息] 多线程管理窗口正在关闭
2025-08-01 12:15:43 [信息] 程序正在退出，开始清理工作...
2025-08-01 12:15:43 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-01 12:15:43 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-01 12:15:43 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-01 12:15:43 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-01 12:15:43 [信息] 程序退出清理工作完成
