2025-08-01 11:18:28 [信息] AWS自动注册工具启动
2025-08-01 11:18:28 [信息] 程序版本: 1.0.0.0
2025-08-01 11:18:28 [信息] 启动时间: 2025-08-01 11:18:28
2025-08-01 11:18:28 [信息] 浏览器模式切换: 无痕 Chrome 模式
2025-08-01 11:18:28 [信息] 线程数量已选择: 1
2025-08-01 11:18:28 [信息] 线程数量选择初始化完成
2025-08-01 11:18:28 [信息] 程序初始化完成
2025-08-01 11:18:41 [信息] 线程数量已选择: 2
2025-08-01 11:18:42 [按钮操作] 选择文件 -> 打开文件选择对话框
2025-08-01 11:18:44 [信息] 已选择文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 11:18:44 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\2025-07-31-阿塞拜疆.txt
2025-08-01 11:18:45 [信息] 成功加载 14 条数据
2025-08-01 11:18:49 [按钮操作] 开始注册 -> 启动注册流程
2025-08-01 11:18:49 [信息] 开始启动多线程注册，线程数量: 2
2025-08-01 11:18:49 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 14
2025-08-01 11:18:49 [信息] 所有线程已停止并清理
2025-08-01 11:18:49 [信息] 正在初始化多线程服务...
2025-08-01 11:18:49 [信息] 美国手机API服务已初始化
2025-08-01 11:18:49 [信息] 手机号码管理器已初始化，服务商: USA，将在第一个线程完成第二页后获取手机号码
2025-08-01 11:18:49 [信息] 多线程服务初始化完成
2025-08-01 11:18:49 [信息] 数据分配完成：共14条数据分配给2个线程
2025-08-01 11:18:49 [信息] 线程1分配到7条数据
2025-08-01 11:18:49 [信息] 线程2分配到7条数据
2025-08-01 11:18:49 [信息] 屏幕工作区域: 1280x672
2025-08-01 11:18:49 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 11:18:49 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-01 11:18:49 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 [信息] 线程1已创建，窗口位置: (0, 0)
2025-08-01 11:18:49 [信息] 屏幕工作区域: 1280x672
2025-08-01 11:18:49 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-01 11:18:49 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-01 11:18:49 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-01 11:18:49 [信息] 线程2已创建，窗口位置: (0, 329)
2025-08-01 11:18:49 [信息] 多线程注册启动成功，共2个线程
2025-08-01 11:18:49 线程1：[信息] 开始启动注册流程
2025-08-01 11:18:49 线程2：[信息] 开始启动注册流程
2025-08-01 11:18:49 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 11:18:49 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 11:18:49 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-01 11:18:49 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-01 11:18:49 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 11:18:49 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-01 11:18:49 [信息] 多线程管理窗口已初始化
2025-08-01 11:18:49 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:18:49 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-01 11:18:49 [信息] 多线程管理窗口已打开
2025-08-01 11:18:49 [信息] 多线程注册启动成功，共2个线程
2025-08-01 11:18:50 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:18:50 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 11:18:50 线程1：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 11:18:50 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 11:18:50 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-01 11:18:50 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 11:18:50 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:18:50 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-01 11:18:50 线程2：[信息] [信息] 使用默认浏览器语言: English (United States) (进度: 0%)
2025-08-01 11:18:50 [信息] 浏览器语言设置: 使用默认语言 en-US
2025-08-01 11:18:50 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:119.0) Gecko/20100101 Firefox/119.0
2025-08-01 11:18:50 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-01 11:18:52 线程2：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 11:18:52 线程1：[信息] [信息] 获取无痕模式页面... (进度: 0%)
2025-08-01 11:18:55 线程2：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 11:18:55 线程1：[信息] [信息] 创建新的无痕模式页面 (进度: 0%)
2025-08-01 11:18:56 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-08-01 11:18:56 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 11:18:56 线程2：[信息] 浏览器启动成功
2025-08-01 11:18:56 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-01 11:18:57 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-01 11:18:57 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 11:18:57 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 11:18:57 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 11:18:57 线程2：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 11:18:57 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-08-01 11:18:57 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-01 11:18:57 线程1：[信息] 浏览器启动成功
2025-08-01 11:18:57 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-01 11:18:57 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-01 11:18:57 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 11:18:57 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-01 11:18:57 线程2：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 11:18:57 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 11:18:57 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-01 11:18:57 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-01 11:18:57 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-01 11:18:57 线程1：[信息] [信息] 多线程模式：为支持语言设置，创建新的浏览器上下文... (进度: 98%)
2025-08-01 11:18:57 线程2：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 11:18:57 线程2：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 11:18:57 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 11:18:57 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0
2025-08-01 11:18:57 [信息] 根据国家代码 AZ 选择时区: America/New_York
2025-08-01 11:18:57 线程1：[信息] [信息] 设置浏览器上下文语言: English (United States) (locale: en-US) (进度: 98%)
2025-08-01 11:18:57 [信息] 浏览器上下文语言设置: 国家代码=AZ, locale=en-US, Accept-Language=en-US,en;q=0.9
2025-08-01 11:18:57 线程1：[信息] [信息] 创建新的浏览器上下文 - UA: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 11:18:57 线程1：[信息] [信息] 设置随机时区: America/New_York (进度: 98%)
2025-08-01 11:18:57 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-01 11:18:58 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 11:18:58 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-01 11:18:58 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-01 11:18:58 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 11:18:58 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-01 11:18:58 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-01 11:18:58 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 11:18:58 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 11:18:58 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 11:18:58 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-01 11:18:58 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-01 11:18:58 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-01 11:19:12 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-01 11:19:12 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 11:19:12 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 11:19:12 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 11:19:12 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 11:19:12 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 11:19:12 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-01 11:19:12 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-01 11:19:12 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-01 11:19:12 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-01 11:19:12 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-01 11:19:13 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-01 11:19:15 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 11:19:15 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 11:19:15 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 11:19:15 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 11:19:15 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:19:15 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 11:19:15 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 11:19:15 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 11:19:15 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 11:19:15 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:19:16 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-01 11:19:16 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-01 11:19:16 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-01 11:19:16 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-01 11:19:16 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-01 11:19:16 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-01 11:19:16 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-01 11:19:16 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-01 11:19:16 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-01 11:19:16 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:19:19 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35154 字节 (进度: 100%)
2025-08-01 11:19:19 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35154字节，复杂度符合要求 (进度: 100%)
2025-08-01 11:19:19 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 11:19:19 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35336 字节 (进度: 100%)
2025-08-01 11:19:19 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35336字节，复杂度符合要求 (进度: 100%)
2025-08-01 11:19:19 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 11:19:21 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"fcsmsf"},"taskId":"50c86bf8-6e86-11f0-a976-bec7f7fa237e"} (进度: 100%)
2025-08-01 11:19:21 线程2：[信息] [信息] 第一页第1次识别结果: fcsmsf → 转换为小写: fcsmsf (进度: 100%)
2025-08-01 11:19:21 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 11:19:21 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"g8gcb3"},"taskId":"50d9acd8-6e86-11f0-8cd2-f6628eeceddd"} (进度: 100%)
2025-08-01 11:19:21 线程1：[信息] [信息] 第一页第1次识别结果: g8gcb3 → 转换为小写: g8gcb3 (进度: 100%)
2025-08-01 11:19:21 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 11:19:21 线程2：[信息] [信息] 已填入验证码: fcsmsf (进度: 100%)
2025-08-01 11:19:21 线程1：[信息] [信息] 已填入验证码: g8gcb3 (进度: 100%)
2025-08-01 11:19:22 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 11:19:22 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 11:19:24 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-01 11:19:24 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-01 11:19:24 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 11:19:24 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 11:19:24 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 11:19:24 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 11:19:24 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 11:19:24 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 11:19:24 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 11:19:24 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 11:19:24 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-01 11:19:24 线程2：[信息] [信息] 第一页第1次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-01 11:19:24 线程2：[信息] [信息] 第一页第2次失败，等待新验证码... (进度: 100%)
2025-08-01 11:19:26 [信息] [线程1] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 11:19:26 线程2：[信息] [信息] 第一页第2次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:19:26 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:19:26
2025-08-01 11:19:29 [信息] [线程1] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 11:19:29 [信息] [线程1] 已写入请求文件: ThreadId:1|Email:<EMAIL>|Time:2025-08-01 11:19:29
2025-08-01 11:19:29 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 31232 字节 (进度: 100%)
2025-08-01 11:19:29 线程2：[信息] [信息] ✅ 图片验证通过：200x71px，31232字节，复杂度符合要求 (进度: 100%)
2025-08-01 11:19:29 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 11:19:30 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"pfmm3"},"taskId":"560e1f7c-6e86-11f0-b641-b26e8748d9fc"} (进度: 100%)
2025-08-01 11:19:30 线程2：[信息] [信息] 第一页第2次识别结果: pfmm3 → 转换为小写: pfmm3 (进度: 100%)
2025-08-01 11:19:30 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 11:19:30 线程2：[信息] [信息] 已填入验证码: pfmm3 (进度: 100%)
2025-08-01 11:19:31 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 11:19:31 [信息] [线程1] 邮箱验证码获取成功: 010823，立即停止重复请求
2025-08-01 11:19:31 [信息] [线程1] 已清理请求文件，停止重复触发
2025-08-01 11:19:31 [信息] [线程1] 已清理响应文件
2025-08-01 11:19:31 线程1：[信息] [信息] 验证码获取成功: 010823，正在自动填入... (进度: 25%)
2025-08-01 11:19:31 线程1：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-01 11:19:33 线程2：[信息] [信息] 第一页第2次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-01 11:19:33 线程2：[信息] [信息] 第一页第3次失败，等待新验证码... (进度: 100%)
2025-08-01 11:19:33 线程1：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-01 11:19:33 线程1：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-01 11:19:33 [信息] 线程1完成第二页事件已处理
2025-08-01 11:19:33 [信息] 线程1完成第二页，开始批量获取手机号码...
2025-08-01 11:19:33 线程1：[信息] [信息] 线程1第二页验证完成，已通知管理器 (进度: 35%)
2025-08-01 11:19:33 [信息] 开始批量获取2个手机号码，服务商: USA
2025-08-01 11:19:33 [信息] 美国API开始批量获取2个手机号码
2025-08-01 11:19:33 [信息] [美国API] 开始批量购买2个手机号码
2025-08-01 11:19:33 [信息] [美国API] 请求购买2个手机号码: https://www.api21k.com/api/v1/buy/create
2025-08-01 11:19:35 线程2：[信息] [信息] 第一页第3次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:19:36 [信息] [美国API] 购买响应: {"code":1,"msg":"购买成功","time":1754018374,"data":{"ordernum":"25080111193550933","api_count":2}}
2025-08-01 11:19:36 [信息] [美国API] 购买成功，订单号: 25080111193550933，数量: 2
2025-08-01 11:19:36 [信息] [美国API] 批量购买成功，订单号: 25080111193550933
2025-08-01 11:19:36 [信息] [美国API] 请求获取手机号码列表: https://www.api21k.com/api/v1/order/api
2025-08-01 11:19:36 线程1：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-01 11:19:36 线程1：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-01 11:19:36 [信息] [美国API] 获取列表响应: {"code":1,"msg":"获取订单API列表成功","time":1754018375,"data":{"url_list":["https:\/\/api.sms8.net","https:\/\/api.smsapi.cc","http:\/\/47.76.194.115"],"list":[{"id":56661342,"app_id":81,"cate_id":2,"type":1,"tel":"18568568671","token":"tb281ani201sttalh1vdt81l7g2veh44xrv","end_time":"2025-10-10 00:00:00","sms_count":0,"voice_count":0,"remark":"","status":0,"api":"https:\/\/api.sms8.net\/api\/record?token=tb281ani201sttalh1vdt81l7g2veh44xrv"},{"id":56661343,"app_id":81,"cate_id":2,"type":1,"tel":"15342044947","token":"mg6p35a1umuah28posl2g99jj9fhizix8qz","end_time":"2025-10-19 00:00:00","sms_count":0,"voice_count":0,"remark":"","status":0,"api":"https:\/\/api.sms8.net\/api\/record?token=mg6p35a1umuah28posl2g99jj9fhizix8qz"}],"total":2}}
2025-08-01 11:19:36 [信息] [美国API] 解析号码: +18568568671
2025-08-01 11:19:36 [信息] [美国API] 解析号码: +15342044947
2025-08-01 11:19:36 [信息] [美国API] 获取号码列表成功，共2个
2025-08-01 11:19:36 [信息] [美国API] 批量获取手机号码成功，共2个
2025-08-01 11:19:36 [信息] 线程1分配美国手机号码: +18568568671
2025-08-01 11:19:36 [信息] 线程2分配美国手机号码: +15342044947
2025-08-01 11:19:36 [信息] 美国API批量获取手机号码成功，共2个
2025-08-01 11:19:36 [信息] 批量获取2个手机号码成功
2025-08-01 11:19:36 线程1：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 11:19:36 线程1：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 11:19:36 线程1：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-01 11:19:37 线程1：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-01 11:19:38 线程2：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 31466 字节 (进度: 100%)
2025-08-01 11:19:38 线程2：[信息] [信息] ✅ 图片验证通过：200x71px，31466字节，复杂度符合要求 (进度: 100%)
2025-08-01 11:19:38 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 11:19:40 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"cttw"},"taskId":"5bbcec28-6e86-11f0-8cd2-f6628eeceddd"} (进度: 100%)
2025-08-01 11:19:40 线程2：[信息] [信息] 第一页第3次识别结果: cttw → 转换为小写: cttw (进度: 100%)
2025-08-01 11:19:40 线程2：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 11:19:40 线程2：[信息] [信息] 已填入验证码: cttw (进度: 100%)
2025-08-01 11:19:40 线程2：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 11:19:40 线程1：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-01 11:19:40 线程1：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-01 11:19:40 线程1：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-01 11:19:42 线程2：[信息] [信息] 第一页第3次图形验证码识别结果错误，等待新验证码 (进度: 100%)
2025-08-01 11:19:42 线程2：[信息] [信息] 第一页图形验证码自动处理失败，转为手动模式 (进度: 100%)
2025-08-01 11:19:45 线程1：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-01 11:19:45 线程1：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-01 11:19:55 线程2：[信息] [信息] 检测到第一页验证码已完成，继续流程 (进度: 100%)
2025-08-01 11:19:55 线程2：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-01 11:19:55 线程2：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-01 11:19:55 线程2：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-01 11:19:55 线程2：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-01 11:19:55 [信息] [线程2] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-01 11:19:55 线程2：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-01 11:19:55 [信息] [线程2] 等待2秒后开始第一次触发...
2025-08-01 11:19:55 线程2：[信息] 账户注册流程已启动: <EMAIL>
2025-08-01 11:19:55 线程2：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-01 11:19:57 [信息] [线程2] 第1次触发邮箱验证码获取...（最多20次）
2025-08-01 11:19:58 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 11:19:57
2025-08-01 11:20:00 [信息] [线程2] 第2次触发邮箱验证码获取...（最多20次）
2025-08-01 11:20:00 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 11:20:00
2025-08-01 11:20:01 线程1：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-01 11:20:02 线程1：[信息] [信息] 数据国家代码为AZ，需要选择Azerbaijan (进度: 38%)
2025-08-01 11:20:03 线程1：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-01 11:20:03 [信息] [线程2] 第3次触发邮箱验证码获取...（最多20次）
2025-08-01 11:20:03 [信息] [线程2] 已写入请求文件: ThreadId:2|Email:<EMAIL>|Time:2025-08-01 11:20:03
2025-08-01 11:20:04 [信息] [线程2] 邮箱验证码获取成功: 365918，立即停止重复请求
2025-08-01 11:20:04 [信息] [线程2] 已清理请求文件，停止重复触发
2025-08-01 11:20:04 [信息] [线程2] 已清理响应文件
2025-08-01 11:20:04 线程2：[信息] [信息] 验证码获取成功: 365918，正在自动填入... (进度: 25%)
2025-08-01 11:20:04 线程2：[信息] [信息] 邮箱验证码获取成功，已取消获取线程 (进度: 25%)
2025-08-01 11:20:04 线程2：[信息] [信息] 验证码已自动填入，正在自动点击验证按钮... (进度: 25%)
2025-08-01 11:20:04 线程2：[信息] [信息] 邮箱验证完成，等待页面跳转... (进度: 25%)
2025-08-01 11:20:04 [信息] 线程2完成第二页事件已处理
2025-08-01 11:20:04 [信息] 线程2完成第二页，手机号码已获取，无需重复获取
2025-08-01 11:20:04 线程2：[信息] [信息] 线程2第二页验证完成，已通知管理器 (进度: 35%)
2025-08-01 11:20:05 线程1：[信息] [信息] 已选择国家: Azerbaijan (进度: 38%)
2025-08-01 11:20:05 线程1：[信息] [信息] 已成功选择国家: Azerbaijan (进度: 38%)
2025-08-01 11:20:05 线程1：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 38%)
2025-08-01 11:20:05 线程1：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 38%)
2025-08-01 11:20:05 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 11:20:06 线程1：[信息] [信息] 已选择国家代码 +1 (进度: 38%)
2025-08-01 11:20:07 线程2：[信息] [信息] 等待密码设置页面加载... (进度: 38%)
2025-08-01 11:20:07 线程2：[信息] [信息] 开始填写密码信息... (进度: 38%)
2025-08-01 11:20:07 线程2：[信息] [信息] 第一个密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 11:20:07 线程2：[信息] [信息] 确认密码输入框已清空并重新填写完成 (进度: 38%)
2025-08-01 11:20:07 线程2：[信息] [信息] 密码填写完成，点击继续按钮... (进度: 38%)
2025-08-01 11:20:07 线程1：[信息] [信息] 开始检查手机号码状态... (进度: 38%)
2025-08-01 11:20:07 线程1：[信息] [信息]  检查页面是否已有手机号码填写... (进度: 38%)
2025-08-01 11:20:07 线程1：[信息] [信息]  页面手机号码输入框当前值: '' (进度: 38%)
2025-08-01 11:20:07 线程1：[信息] [信息]  页面手机号码输入框为空，需要手动输入 (进度: 38%)
2025-08-01 11:20:07 线程1：[信息] [信息]  最终决定：显示手动输入弹窗 (进度: 38%)
2025-08-01 11:20:07 线程1：[信息] [信息] 🔴 请手动输入手机号码，然后点击【继续注册】 (进度: 38%)
2025-08-01 11:20:09 线程2：[信息] [信息] 密码设置完成，等待页面跳转... (进度: 38%)
2025-08-01 11:20:12 线程2：[信息] [信息] 第三页完成，进入第3.5页（账户类型确认页面）... (进度: 38%)
2025-08-01 11:20:12 线程2：[信息] [信息] 等待账户类型确认页面加载... (进度: 38%)
2025-08-01 11:20:12 线程2：[信息] [信息] 开始处理账户类型确认... (进度: 38%)
2025-08-01 11:20:17 线程2：[信息] [信息] 已点击Choose paid plan按钮，账户类型确认完成 (进度: 38%)
2025-08-01 11:20:17 线程2：[信息] [信息] 账户类型确认完成，进入联系信息页面... (进度: 38%)
2025-08-01 11:20:32 线程2：[信息] [信息] 第3.5页完成，页面已跳转到第4页 (进度: 38%)
2025-08-01 11:20:33 线程2：[信息] [信息] 数据国家代码为AZ，需要选择Azerbaijan (进度: 38%)
2025-08-01 11:20:34 线程2：[信息] [信息] 已点击国家/地区选择器，正在展开列表... (进度: 38%)
2025-08-01 11:20:36 线程2：[信息] [信息] 已选择国家: Azerbaijan (进度: 38%)
2025-08-01 11:20:36 线程2：[信息] [信息] 已成功选择国家: Azerbaijan (进度: 38%)
2025-08-01 11:20:36 线程2：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 38%)
2025-08-01 11:20:36 线程2：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 38%)
2025-08-01 11:20:36 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 38%)
2025-08-01 11:20:37 线程2：[信息] [信息] 已选择国家代码 +1 (进度: 38%)
2025-08-01 11:20:38 线程2：[信息] [信息] 开始检查手机号码状态... (进度: 38%)
2025-08-01 11:20:38 线程2：[信息] [信息]  检查页面是否已有手机号码填写... (进度: 38%)
2025-08-01 11:20:38 线程2：[信息] [信息]  页面手机号码输入框当前值: '' (进度: 38%)
2025-08-01 11:20:38 线程2：[信息] [信息]  页面手机号码输入框为空，需要手动输入 (进度: 38%)
2025-08-01 11:20:38 线程2：[信息] [信息]  最终决定：显示手动输入弹窗 (进度: 38%)
2025-08-01 11:20:38 线程2：[信息] [信息] 🔴 请手动输入手机号码，然后点击【继续注册】 (进度: 38%)
2025-08-01 11:20:56 线程1：[信息] [信息]  继续注册被调用，当前状态: WaitingForPhoneInput，当前步骤: 4 (进度: 38%)
2025-08-01 11:20:56 线程1：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-08-01 11:20:56 线程1：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-08-01 11:20:56 线程1：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-08-01 11:20:56 线程1：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 38%)
2025-08-01 11:20:56 线程1：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-01 11:20:56 线程1：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-01 11:20:56 线程1：[信息] [信息] 正在从页面读取手机号码... (进度: 100%)
2025-08-01 11:20:56 线程1：[信息] [信息] 从页面读取并保存手机号码: 18568568671（不含区号） (进度: 100%)
2025-08-01 11:20:56 线程2：[信息] [信息]  继续注册被调用，当前状态: WaitingForPhoneInput，当前步骤: 4 (进度: 38%)
2025-08-01 11:20:56 线程2：[信息] [信息]  进行智能页面检测... (进度: 38%)
2025-08-01 11:20:56 线程2：[信息] [信息] 🔍 开始智能页面检测（按钮优先策略）... (进度: 38%)
2025-08-01 11:20:56 线程2：[信息] [信息] 📋 获取页面所有按钮和链接元素... (进度: 38%)
2025-08-01 11:20:57 线程1：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-01 11:20:58 线程2：[信息] [信息] 🎯 找到匹配按钮: 'Agree and Continue (step 2 of 5)' → 第4页 (进度: 38%)
2025-08-01 11:20:58 线程2：[信息] [信息] ✅ 直接确认为第4页 (进度: 100%)
2025-08-01 11:20:58 线程2：[信息] [信息]  智能检测到当前在第4页 (进度: 100%)
2025-08-01 11:20:58 线程2：[信息] [信息] 正在从页面读取手机号码... (进度: 100%)
2025-08-01 11:20:58 线程2：[信息] [信息] 从页面读取并保存手机号码: 15342044947（不含区号） (进度: 100%)
2025-08-01 11:20:58 线程2：[信息] [信息] 联系信息完成，等待页面加载... (进度: 100%)
2025-08-01 11:21:00 线程1：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-01 11:21:01 线程1：[信息] [信息] 正在选择月份: April (进度: 100%)
2025-08-01 11:21:01 线程1：[信息] [信息] 已选择月份（标准选项）: April (进度: 100%)
2025-08-01 11:21:01 线程2：[信息] [信息] 进入付款信息页面... (进度: 100%)
2025-08-01 11:21:02 线程1：[信息] [信息] 正在选择年份: 2028 (进度: 100%)
2025-08-01 11:21:02 线程2：[信息] [信息] 正在选择月份: September (进度: 100%)
2025-08-01 11:21:02 线程1：[信息] [信息] 已选择年份（标准选项）: 2028 (进度: 100%)
2025-08-01 11:21:02 线程2：[信息] [信息] 已选择月份（标准选项）: September (进度: 100%)
2025-08-01 11:21:03 线程1：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-01 11:21:03 线程1：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-01 11:21:03 线程1：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 100%)
2025-08-01 11:21:03 线程1：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 100%)
2025-08-01 11:21:03 线程2：[信息] [信息] 正在选择年份: 2027 (进度: 100%)
2025-08-01 11:21:03 线程2：[信息] [信息] 已选择年份（标准选项）: 2027 (进度: 100%)
2025-08-01 11:21:04 线程2：[信息] [信息] 付款信息完成，进入验证码验证页面... (进度: 100%)
2025-08-01 11:21:04 线程2：[信息] [信息] 开始填写验证码验证页面... (进度: 100%)
2025-08-01 11:21:04 线程2：[信息] [信息] 美国API模式：使用固定的+1区号 (进度: 100%)
2025-08-01 11:21:04 线程2：[信息] [信息] 正在选择国家代码 +1 (美国 (United States) +1)... (进度: 100%)
2025-08-01 11:21:07 线程1：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-01 11:21:08 线程2：[信息] [信息] 已点击国家代码按钮，正在展开列表... (进度: 100%)
2025-08-01 11:21:08 线程1：[信息] [信息] 已选择国家代码: +1 (进度: 100%)
2025-08-01 11:21:08 线程1：[信息] [信息] 已清空并重新填写手机号码: 18568568671 (进度: 100%)
2025-08-01 11:21:08 线程1：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-01 11:21:09 线程2：[信息] [信息] 已选择国家代码: +1 (进度: 100%)
2025-08-01 11:21:09 线程2：[信息] [信息] 已清空并重新填写手机号码: 15342044947 (进度: 100%)
2025-08-01 11:21:09 线程2：[信息] [信息] 已点击发送验证码按钮 (进度: 100%)
2025-08-01 11:21:10 线程1：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-01 11:21:10 线程1：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-01 11:21:10 线程1：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-01 11:21:10 线程1：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-01 11:21:11 线程2：[信息] [信息] 🔍 检查是否出现验证手机区号错误... (进度: 100%)
2025-08-01 11:21:11 线程2：[信息] [信息] ✅ 未检测到验证手机区号错误，继续执行 (进度: 100%)
2025-08-01 11:21:11 线程2：[信息] [信息] 图形验证码自动模式：开始自动处理图形验证码... (进度: 100%)
2025-08-01 11:21:11 线程2：[信息] [信息] 第六页点击发送验证码后，等待图形验证码出现... (进度: 100%)
2025-08-01 11:21:13 线程1：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-01 11:21:13 线程1：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:21:14 线程2：[信息] [信息] 第六页图形验证码自动识别模式，开始处理... (进度: 100%)
2025-08-01 11:21:14 线程2：[信息] [信息] 第六页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-01 11:21:17 线程1：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35271 字节 (进度: 100%)
2025-08-01 11:21:17 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35271字节，复杂度符合要求 (进度: 100%)
2025-08-01 11:21:17 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 11:21:18 线程2：[信息] [信息] 第六页已从iframe截取验证码图片，大小: 35190 字节 (进度: 100%)
2025-08-01 11:21:18 线程2：[信息] [信息] ✅ 图片验证通过：201x71px，35190字节，复杂度符合要求 (进度: 100%)
2025-08-01 11:21:18 线程2：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-01 11:21:19 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"pzrmpm"},"taskId":"96ccbd34-6e86-11f0-a976-bec7f7fa237e"} (进度: 100%)
2025-08-01 11:21:19 线程1：[信息] [信息] 第六页第1次识别结果: pzrmpm → 转换为小写: pzrmpm (进度: 100%)
2025-08-01 11:21:19 线程1：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 11:21:19 线程1：[信息] [信息] 第六页已填入验证码: pzrmpm (进度: 100%)
2025-08-01 11:21:19 线程1：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 11:21:20 线程2：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"shz4zr"},"taskId":"96f41212-6e86-11f0-b5bc-5a3e33407522"} (进度: 100%)
2025-08-01 11:21:20 线程2：[信息] [信息] 第六页第1次识别结果: shz4zr → 转换为小写: shz4zr (进度: 100%)
2025-08-01 11:21:20 线程2：[信息] [信息] 第六页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-01 11:21:20 线程2：[信息] [信息] 第六页已填入验证码: shz4zr (进度: 100%)
2025-08-01 11:21:20 线程2：[信息] [信息] 第六页已点击iframe内Submit按钮 (进度: 100%)
2025-08-01 11:21:22 线程1：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-01 11:21:22 线程1：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-01 11:21:23 线程2：[信息] [信息] 第1次图形验证码识别成功 (进度: 100%)
2025-08-01 11:21:23 线程2：[信息] [信息] 第六页图形验证码自动完成，检查验证结果... (进度: 100%)
2025-08-01 11:21:25 线程1：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-01 11:21:26 线程2：[信息] [信息] 第六页图形验证码验证成功，进入第七页 (进度: 100%)
2025-08-01 11:21:28 线程1：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-01 11:21:28 线程1：[信息] [信息] 第七页手动模式：请手动输入验证码 (进度: 100%)
2025-08-01 11:21:28 线程1：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-01 11:21:28 线程1：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-01 11:21:28 线程1：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-01 11:21:28 线程1：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 7033位 (进度: 100%)
2025-08-01 11:21:28 线程1：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-01 11:21:28 线程1：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-01 11:21:28 线程1：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-01 11:21:29 线程2：[信息] [信息] 开始处理第七页 - Continue (step 4 of 5) 页面 (进度: 100%)
2025-08-01 11:21:29 线程2：[信息] [信息] 第七页手动模式：请手动输入验证码 (进度: 100%)
2025-08-01 11:21:29 线程2：[信息] [信息] 🔧 [DEBUG] 开始检测第七页手机验证码是否已填写... (进度: 100%)
2025-08-01 11:21:29 线程2：[信息] [信息] 🔧 [DEBUG] 检测到第七页特征: *:has-text('step 4 of 5') (进度: 100%)
2025-08-01 11:21:29 线程2：[信息] [信息] 🔧 [DEBUG] 通过父元素文本确认为第七页验证码输入框 (进度: 100%)
2025-08-01 11:21:29 线程2：[信息] [信息] ✅ 检测到第七页手机验证码已填写: 7013位 (进度: 100%)
2025-08-01 11:21:29 线程2：[信息] [信息] 第七页：检测到验证码已填写，查找Continue按钮... (进度: 100%)
2025-08-01 11:21:29 线程2：[信息] [信息] 第七页：检测到Continue (step 4 of 5)按钮，自动点击... (进度: 100%)
2025-08-01 11:21:29 线程2：[信息] [信息] 第七页：已点击Continue (step 4 of 5)按钮，等待页面跳转... (进度: 100%)
2025-08-01 11:21:32 线程1：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-01 11:21:32 线程1：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-01 11:21:32 线程2：[信息] [信息] 第七页Continue按钮处理完成，开始执行后续注册流程... (进度: 100%)
2025-08-01 11:21:32 线程2：[信息] [信息] 验证码验证完成，等待页面加载... (进度: 100%)
2025-08-01 11:22:00 线程1：[信息] [信息] 已点击完成注册按钮，正在处理... (进度: 100%)
2025-08-01 11:22:01 线程1：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-01 11:22:03 线程2：[信息] [信息] 点击完成注册按钮失败: Timeout 30000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Button, new() { Name = "Complete sign up" }) (进度: 100%)
2025-08-01 11:22:03 线程2：[信息] [信息] 等待完成注册页面加载... (进度: 100%)
2025-08-01 11:22:04 线程1：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-01 11:22:04 线程1：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-01 11:22:06 线程2：[信息] [信息] 完成注册页面加载完成 (进度: 100%)
2025-08-01 11:22:06 线程2：[信息] [信息] 🔍 正在查找'Go to the AWS Management Console'链接... (进度: 100%)
2025-08-01 11:22:08 线程1：[信息] [信息] 🖱️ 已点击跳转管理控制台链接 (进度: 100%)
2025-08-01 11:22:08 线程1：[信息] [信息] 🔗 通过导航进入IAM安全凭证页面... (进度: 100%)
2025-08-01 11:22:08 线程1：[信息] [信息] ⏳ 等待管理控制台页面加载，智能检测'更多'按钮（超时20秒）... (进度: 100%)
2025-08-01 11:22:11 线程2：[信息] [信息] ❌ 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible (进度: 100%)
2025-08-01 11:22:11 [信息] 控制台按钮处理失败: Timeout 5000ms exceeded.
Call log:
  - waiting for GetByRole(AriaRole.Link, new() { Name = "Go to the AWS Management Console" }) to be visible
2025-08-01 11:22:11 线程2：[信息] [信息] ⚠️ 密钥提取失败，按原本逻辑完成注册 (进度: 100%)
2025-08-01 11:22:11 [信息] 密钥提取失败，按原本逻辑完成注册
2025-08-01 11:22:11 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：G5i6691jFRu6 ③AWS密码：VFv0DeSV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息： (进度: 100%)
2025-08-01 11:22:11 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：G5i6691jFRu6 ③AWS密码：VFv0DeSV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 11:22:11 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：G5i6691jFRu6 ③AWS密码：VFv0DeSV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 11:22:11 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：G5i6691jFRu6 ③AWS密码：VFv0DeSV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 11:22:11 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：G5i6691jFRu6 ③AWS密码：VFv0DeSV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 11:22:11 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：G5i6691jFRu6 ③AWS密码：VFv0DeSV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：
2025-08-01 11:22:11 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-01 11:22:11 线程2：[信息] 账户注册完成(无密钥)，等待后续操作: <EMAIL>
2025-08-01 11:22:11 线程2：[信息] [信息] ✅ 注册成功（密钥提取失败） (进度: 98%)
2025-08-01 11:22:11 [信息] 注册完成（密钥提取失败）
2025-08-01 11:22:11 [信息] 开始处理数据完成事件: <EMAIL>
2025-08-01 11:22:11 [信息] 已将完成数据移动到注册成功区域: <EMAIL>
2025-08-01 11:22:11 [信息] 已完成数据移除: <EMAIL>
2025-08-01 11:22:11 [信息] 数据完成事件处理完毕: <EMAIL>
2025-08-01 11:22:11 线程2：[信息] 最终完成: 注册完成，无法提取密钥: <EMAIL> (类型: WithoutKeys)
2025-08-01 11:22:11 [信息] 线程2数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-01 11:22:11 线程2：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-01 11:22:11 线程2：[信息] 已继续
2025-08-01 11:22:11 [信息] 线程2已继续
2025-08-01 11:22:12 [信息] 获取线程2当前数据: 无
2025-08-01 11:22:12 线程2：[信息] 终止时没有正在处理的数据
2025-08-01 11:22:12 线程2：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-01 11:22:12 线程2：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-01 11:22:12 线程2：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：G5i6691jFRu6 ③AWS密码：VFv0DeSV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-01 11:22:12 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：G5i6691jFRu6 ③AWS密码：VFv0DeSV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:22:12 线程2：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：G5i6691jFRu6 ③AWS密码：VFv0DeSV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:22:12 [信息] 线程2请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：G5i6691jFRu6 ③AWS密码：VFv0DeSV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:22:12 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：G5i6691jFRu6 ③AWS密码：VFv0DeSV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:22:12 [信息] 线程2剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：G5i6691jFRu6 ③AWS密码：VFv0DeSV ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:22:12 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-01 11:22:12 线程2：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_2_20250801_111849
2025-08-01 11:22:12 线程2：[信息] 已终止
2025-08-01 11:22:12 [信息] 线程2已终止
2025-08-01 11:22:12 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:22:12 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-01 11:22:12 [信息] 线程2已终止
2025-08-01 11:22:15 线程1：[信息] [信息] ✅ 找到'更多'按钮，页面加载完成 (进度: 100%)
2025-08-01 11:22:15 线程1：[信息] [信息] 🖱️ 正在点击更多按钮... (进度: 100%)
2025-08-01 11:22:15 线程1：[信息] [信息] ✅ 成功点击更多按钮 (进度: 100%)
2025-08-01 11:22:15 [信息] 成功点击更多按钮
2025-08-01 11:22:16 线程1：[信息] [信息] 🖱️ 正在点击账户信息按钮... (进度: 100%)
2025-08-01 11:22:17 线程1：[信息] [信息] ✅ 成功点击账户信息按钮 (进度: 100%)
2025-08-01 11:22:17 [信息] 成功点击账户信息按钮
2025-08-01 11:22:18 线程1：[信息] [信息] 🖱️ 正在点击'安全凭证'菜单项... (进度: 100%)
2025-08-01 11:22:18 线程1：[信息] [信息] ✅ 使用TestId定位到'安全凭证'链接 (进度: 100%)
2025-08-01 11:22:18 [信息] 使用TestId定位到'安全凭证'链接
2025-08-01 11:22:24 线程1：[信息] [信息] ✅ 成功点击'安全凭证'菜单项 (进度: 100%)
2025-08-01 11:22:24 [信息] 成功点击'安全凭证'链接
2025-08-01 11:22:24 线程1：[信息] [信息] ⏳ 等待IAM安全凭证页面加载（20秒超时，循环检测账单问题和创建密钥按钮）... (进度: 100%)
2025-08-01 11:22:27 线程1：[信息] [信息] ❌ 检测到账单问题页面跳转 (进度: 100%)
2025-08-01 11:22:27 线程1：[信息] [信息] ❌ 检测到账单问题，处理账单问题流程 (进度: 100%)
2025-08-01 11:22:27 线程1：[信息] [信息] ⚠️ 处理账单问题，终止注册 (进度: 100%)
2025-08-01 11:22:27 [信息] 检测到账单问题，开始处理
2025-08-01 11:22:27 线程1：[信息] [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：8JB47D7T3sOS ③AWS密码：m4mihibW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单 (进度: 100%)
2025-08-01 11:22:27 [信息] 账单问题注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：8JB47D7T3sOS ③AWS密码：m4mihibW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-01 11:22:27 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：8JB47D7T3sOS ③AWS密码：m4mihibW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-01 11:22:27 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：8JB47D7T3sOS ③AWS密码：m4mihibW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-01 11:22:27 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：8JB47D7T3sOS ③AWS密码：m4mihibW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-01 11:22:27 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：8JB47D7T3sOS ③AWS密码：m4mihibW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //提示付款账单
2025-08-01 11:22:27 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:22:27 [信息] 多线程状态已重置
2025-08-01 11:22:27 线程1：[信息] 账户注册完成(账单问题)，等待后续操作: <EMAIL>
2025-08-01 11:22:27 线程1：[信息] [信息] ✅ 注册成功，账户提示账单 (进度: 100%)
2025-08-01 11:22:27 [信息] 注册完成 - 账单提示处理
2025-08-01 11:22:27 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-01 11:22:27 [信息] 开始处理账单问题数据完成事件: <EMAIL>
2025-08-01 11:22:27 [信息] 已将账单问题数据移动到注册成功区域: <EMAIL>
2025-08-01 11:22:27 [信息] 已完成账单问题数据移除: <EMAIL>
2025-08-01 11:22:27 [信息] 账单问题数据完成事件处理完毕: <EMAIL>
2025-08-01 11:22:27 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:22:27 [信息] 多线程状态已重置
2025-08-01 11:22:27 线程1：[信息] 最终完成: 注册完成，账单提示无法提取密钥: <EMAIL> (类型: WithBillingIssue)
2025-08-01 11:22:27 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:22:27 [信息] 多线程状态已重置
2025-08-01 11:22:27 [信息] 线程1数据完成事件已通知主窗口并最终完成: <EMAIL>
2025-08-01 11:22:27 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:22:27 [信息] 多线程状态已重置
2025-08-01 11:22:27 线程1：[信息] [信息] 图形验证码处理完成，请手动输入手机验证码... (进度: 100%)
2025-08-01 11:22:27 线程1：[信息] 已继续
2025-08-01 11:22:27 [信息] 线程1已继续
2025-08-01 11:22:40 [信息] 获取线程1当前数据: 无
2025-08-01 11:22:40 线程1：[信息] 终止时没有正在处理的数据
2025-08-01 11:22:40 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:22:40 [信息] 多线程状态已重置
2025-08-01 11:22:40 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:22:40 [信息] 多线程状态已重置
2025-08-01 11:22:40 线程1：[信息] [信息] 注册已终止 (进度: 100%)
2025-08-01 11:22:40 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:22:40 [信息] 多线程状态已重置
2025-08-01 11:22:40 线程1：[信息] [信息] 所有自动线程已停止 (进度: 100%)
2025-08-01 11:22:41 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:22:41 [信息] 多线程状态已重置
2025-08-01 11:22:41 线程1：[信息] [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：8JB47D7T3sOS ③AWS密码：m4mihibW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止 (进度: 100%)
2025-08-01 11:22:41 [信息] 注册信息已复制到剪贴板：①邮箱账号：<EMAIL> ②邮箱密码：8JB47D7T3sOS ③AWS密码：m4mihibW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:22:41 线程1：[信息] 收到剪贴板保存请求: ①邮箱账号：<EMAIL> ②邮箱密码：8JB47D7T3sOS ③AWS密码：m4mihibW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:22:41 [信息] 线程1请求保存剪贴板信息: ①邮箱账号：<EMAIL> ②邮箱密码：8JB47D7T3sOS ③AWS密码：m4mihibW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:22:41 [信息] 已将剪贴板信息保存到成功数据区域: ①邮箱账号：<EMAIL> ②邮箱密码：8JB47D7T3sOS ③AWS密码：m4mihibW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:22:41 [信息] 线程1剪贴板信息已保存: ①邮箱账号：<EMAIL> ②邮箱密码：8JB47D7T3sOS ③AWS密码：m4mihibW ④访问密钥： ⑤秘密访问密钥： ⑥MFA信息：   //手动终止
2025-08-01 11:22:41 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:22:41 [信息] 多线程状态已重置
2025-08-01 11:22:41 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 100%)
2025-08-01 11:22:41 线程1：[信息] 临时目录已清理: C:\Users\<USER>\AppData\Local\Temp\AWS_Thread_1_20250801_111849
2025-08-01 11:22:41 [信息] 所有线程已完成，通知主窗口重置状态
2025-08-01 11:22:41 [信息] 多线程状态已重置
2025-08-01 11:22:41 线程1：[信息] 已终止
2025-08-01 11:22:41 [信息] 线程1已终止
2025-08-01 11:22:41 [信息] UniformGrid列数已更新为: 1
2025-08-01 11:22:41 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-01 11:22:41 [信息] 线程1已终止
2025-08-01 11:22:42 [信息] 多线程窗口引用已清理
2025-08-01 11:22:42 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-01 11:22:42 [信息] 多线程管理窗口正在关闭
2025-08-01 11:22:44 [信息] 程序正在退出，开始清理工作...
2025-08-01 11:22:44 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-01 11:22:44 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-01 11:22:44 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-01 11:22:44 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-01 11:22:44 [信息] 程序退出清理工作完成
