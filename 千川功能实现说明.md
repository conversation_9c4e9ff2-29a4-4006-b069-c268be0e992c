# 千川手机号码服务商功能实现说明

## 概述
为AWS注册工具添加了千川手机号码服务商支持，作为第二个选项（位于榴莲和美国之间）。

## 主要特性

### 1. 基本配置
- **服务商位置**: 第二个选项（榴莲 → 千川 → 美国 → 手动模式）
- **区号**: 固定使用 +43（奥地利）
- **配置方式**: 使用 config.json 中的配置，界面无需输入框
- **API特点**: 不支持批量获取号码，需要逐个获取

### 2. API接口实现
- **获取手机号**: `GET https://api.qc86.shop/api/getPhone`
- **获取验证码**: `GET https://api.qc86.shop/api/getCode`
- **拉黑手机号**: `GET https://api.qc86.shop/api/phoneCollectAdd`

### 3. 配置参数
在 `config.json` 中添加了以下配置：
```json
{
  "PhoneApi": {
    "QianchuanToken": "170154-*-c774223f-7655-4425-9871-c8bc459d4a6b",
    "QianchuanChannelId": "7",
    "QianchuanBaseUrl": "https://api.qc86.shop"
  }
}
```

### 4. 重试机制
- **获取手机号**: 3次重试，每次间隔1秒
- **获取验证码**: 3次重试，每次间隔1秒
- **验证码提取**: 从 `modle` 字段提取4位连续数字
- **验证码转换**: 自动转换为小写

### 5. 多线程支持
- **号码分配**: 根据线程数量逐个获取号码
- **失败处理**: 未分配到号码的线程自动转为手动模式
- **黑名单管理**: 统一的黑名单管理器，定时批量拉黑

## 文件修改清单

### 1. 核心服务类
- **新增**: `Services/QianchuanPhoneApiService.cs` - 千川API服务实现
- **修改**: `Services/ConfigService.cs` - 添加千川配置和枚举
- **修改**: `Services/AutomationService.cs` - 集成千川服务和区号选择
- **修改**: `Services/MultiThreadManager.cs` - 多线程千川支持
- **修改**: `Services/MultiThreadPhoneNumberManager.cs` - 千川号码管理
- **修改**: `Services/MultiThreadVerificationCodeManager.cs` - 千川验证码管理

### 2. 界面文件
- **修改**: `MainWindow.xaml` - 添加千川选项按钮
- **修改**: `MainWindow.xaml.cs` - 千川模式处理逻辑

### 3. 配置文件
- **修改**: `config.json` - 添加千川API配置

## 技术实现要点

### 1. 枚举更新
```csharp
public enum PhoneProvider
{
    Durian = 0,      // 榴莲
    Qianchuan = 1,   // 千川
    USA = 2,         // 美国
    Manual = 3       // 手动模式
}
```

### 2. 区号自动选择
在第四页和第六页自动选择奥地利（+43）区号：
```csharp
else if (phoneConfig.Provider == PhoneProvider.Qianchuan)
{
    countryInfo = CountryCodeService.GetCountryByCode("at");
    StatusChanged?.Invoke("千川API模式：使用固定的+43奥地利区号");
}
```

### 3. 验证码提取
从API返回的 `modle` 字段提取4位数字验证码：
```csharp
private string ExtractVerificationCode(string modle)
{
    if (string.IsNullOrEmpty(modle))
        return string.Empty;

    var match = Regex.Match(modle, @"\d{4}");
    return match.Success ? match.Value : string.Empty;
}
```

### 4. 多线程号码分配
千川不支持批量获取，需要逐个获取：
```csharp
// 千川API：不支持批量获取，需要逐个获取
for (int i = 0; i < threadCount; i++)
{
    int threadId = i + 1;
    var result = await _qianchuanPhoneApiService.GetPhoneNumberAsync();
    if (result.Success && result.PhoneInfo != null)
    {
        _threadQianchuanPhoneNumbers[threadId] = result.PhoneInfo;
        successCount++;
    }
    await Task.Delay(1000); // 避免API频率限制
}
```

### 5. 黑名单管理
实现了统一的黑名单管理器：
- 定时批量拉黑（每30秒检查一次）
- 支持立即拉黑
- 程序退出时自动清理

## 使用说明

### 1. 配置设置
1. 在 `config.json` 中配置千川API参数
2. 在界面中选择"千川"模式
3. 保存配置

### 2. 单线程使用
1. 选择千川模式
2. 启动注册流程
3. 系统自动使用+43区号和千川API

### 3. 多线程使用
1. 选择千川模式
2. 设置线程数量
3. 启动多线程注册
4. 系统为每个线程分配独立的千川号码

## 注意事项

1. **API限制**: 千川API不支持批量获取，多线程时会逐个获取号码
2. **区号固定**: 千川模式固定使用+43奥地利区号
3. **配置要求**: 需要在config.json中正确配置token和channelId
4. **重试机制**: 获取失败时有自动重试，最终失败会转为手动模式
5. **黑名单**: 使用完的号码会自动加入黑名单，避免重复使用

## 测试建议

1. **单线程测试**: 验证千川API调用和区号选择
2. **多线程测试**: 验证号码分配和并发处理
3. **失败处理**: 测试API失败时的重试和降级机制
4. **配置测试**: 验证不同配置参数的效果
