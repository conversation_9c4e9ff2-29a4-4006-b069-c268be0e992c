using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.RegularExpressions;

namespace AWSAutoRegister.Services
{
    public class QianchuanPhoneApiService : IDisposable
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private readonly string _token;
        private readonly string _channelId;
        private readonly LogService _logService;

        public QianchuanPhoneApiService(string baseUrl, string token, string channelId)
        {
            _httpClient = new HttpClient();
            // 设置超时时间为10秒，避免长时间等待
            _httpClient.Timeout = TimeSpan.FromSeconds(10);
            _baseUrl = baseUrl;
            _token = token;
            _channelId = channelId;
            _logService = LogService.Instance;
        }

        public class QianchuanApiResponse<T>
        {
            [JsonPropertyName("status")]
            public int Status { get; set; }

            [JsonPropertyName("success")]
            public bool Success { get; set; }

            [JsonPropertyName("msg")]
            public string Message { get; set; } = string.Empty;

            [JsonPropertyName("data")]
            public T? Data { get; set; }

            [JsonPropertyName("t")]
            public string T { get; set; } = string.Empty;
        }

        public class QianchuanPhoneData
        {
            [JsonPropertyName("code")]
            public int Code { get; set; }

            [JsonPropertyName("desc")]
            public string? Desc { get; set; }

            [JsonPropertyName("message")]
            public string Message { get; set; } = string.Empty;

            [JsonPropertyName("mobile")]
            public string Mobile { get; set; } = string.Empty;

            [JsonPropertyName("smsTask")]
            public QianchuanSmsTask? SmsTask { get; set; }

            [JsonPropertyName("refreshTime")]
            public int RefreshTime { get; set; }
        }

        public class QianchuanSmsTask
        {
            [JsonPropertyName("id")]
            public int Id { get; set; }

            [JsonPropertyName("phoneNo")]
            public string PhoneNo { get; set; } = string.Empty;

            [JsonPropertyName("projectId")]
            public int ProjectId { get; set; }

            [JsonPropertyName("startTime")]
            public string StartTime { get; set; } = string.Empty;

            [JsonPropertyName("status")]
            public int Status { get; set; }

            [JsonPropertyName("uid")]
            public int Uid { get; set; }
        }

        public class QianchuanCodeData
        {
            [JsonPropertyName("code")]
            public string Code { get; set; } = string.Empty;

            [JsonPropertyName("message")]
            public string Message { get; set; } = string.Empty;

            [JsonPropertyName("modle")]
            public string Modle { get; set; } = string.Empty;
        }

        public class QianchuanPhoneInfo
        {
            public string FullNumber { get; set; } = string.Empty;
            public string CountryCode { get; set; } = "+43"; // 固定为奥地利区号
            public string LocalNumber { get; set; } = string.Empty;
        }

        /// <summary>
        /// 获取手机号码（单条）- 千川不支持批量获取
        /// </summary>
        public async Task<(bool Success, QianchuanPhoneInfo? PhoneInfo, string Message)> GetPhoneNumberAsync()
        {
            return await ApiRetryService.ExecuteWithRetryTupleAsync(
                async () => await GetPhoneNumberInternalAsync(),
                "获取手机号码",
                "千川API"
            );
        }

        /// <summary>
        /// 内部获取手机号码方法（不包含重试逻辑）
        /// </summary>
        private async Task<(bool Success, QianchuanPhoneInfo? PhoneInfo, string Message)> GetPhoneNumberInternalAsync()
        {
            var url = $"{_baseUrl}/api/getPhone?token={_token}&channelId={_channelId}&operator=5";

            _logService.LogInfo($"[千川API] 请求URL: {url}");

            var response = await _httpClient.GetStringAsync(url);

            _logService.LogInfo($"[千川API] 响应内容: {response}");

            var result = JsonSerializer.Deserialize<QianchuanApiResponse<QianchuanPhoneData>>(response);

            if (result?.Success == true && result.Status == 200 && result.Data != null && !string.IsNullOrEmpty(result.Data.Mobile))
            {
                var phoneInfo = new QianchuanPhoneInfo
                {
                    FullNumber = $"+43{result.Data.Mobile}",
                    CountryCode = "+43",
                    LocalNumber = result.Data.Mobile
                };

                _logService.LogInfo($"[千川API] 获取手机号码成功: {phoneInfo.FullNumber}");
                return (true, phoneInfo, "获取手机号码成功");
            }
            else
            {
                var errorMsg = result?.Message ?? "未知错误";
                _logService.LogError($"[千川API] 获取手机号码失败: {errorMsg}");
                return (false, null, $"获取手机号码失败: {errorMsg}");
            }
        }

        /// <summary>
        /// 获取验证码
        /// </summary>
        public async Task<(bool Success, string? VerificationCode, string Message)> GetVerificationCodeAsync(string phoneNumber)
        {
            // 移除+43前缀，只保留本地号码
            var localPhoneNumber = phoneNumber.StartsWith("+43") ? phoneNumber.Substring(3) : phoneNumber;

            return await ApiRetryService.ExecuteWithRetryTupleAsync(
                async () => await GetVerificationCodeInternalAsync(localPhoneNumber),
                "获取验证码",
                "千川API"
            );
        }

        /// <summary>
        /// 内部获取验证码方法（不包含重试逻辑）
        /// </summary>
        private async Task<(bool Success, string? VerificationCode, string Message)> GetVerificationCodeInternalAsync(string localPhoneNumber)
        {
            var url = $"{_baseUrl}/api/getCode?token={_token}&channelId={_channelId}&phoneNum={localPhoneNumber}";

            _logService.LogInfo($"[千川API] 获取验证码请求: {localPhoneNumber}");

            var response = await _httpClient.GetStringAsync(url);
            _logService.LogInfo($"[千川API] 验证码响应: {response}");

            var result = JsonSerializer.Deserialize<QianchuanApiResponse<QianchuanCodeData>>(response);

            if (result?.Success == true && result.Status == 200 && result.Data != null)
            {
                // 从modle字段提取4位连续数字验证码
                var verificationCode = ExtractVerificationCode(result.Data.Modle);
                if (!string.IsNullOrEmpty(verificationCode))
                {
                    _logService.LogInfo($"[千川API] 获取验证码成功: {verificationCode}");
                    return (true, verificationCode, "获取验证码成功");
                }
                else
                {
                    _logService.LogWarning($"[千川API] 未能从响应中提取验证码: {result.Data.Modle}");
                    return (false, null, "暂未查询到验证码，请稍后再试");
                }
            }
            else
            {
                var errorMsg = result?.Message ?? "未知错误";
                _logService.LogError($"[千川API] 获取验证码失败: {errorMsg}");
                return (false, null, $"获取验证码失败: {errorMsg}");
            }
        }

        /// <summary>
        /// 从modle字段提取4位连续数字验证码
        /// </summary>
        private string ExtractVerificationCode(string modle)
        {
            if (string.IsNullOrEmpty(modle))
                return string.Empty;

            // 查找4位连续数字
            var match = Regex.Match(modle, @"\d{4}");
            return match.Success ? match.Value : string.Empty;
        }

        /// <summary>
        /// 将手机号码加入黑名单
        /// </summary>
        public async Task<(bool Success, string Message)> AddToBlacklistAsync(string phoneNumber)
        {
            try
            {
                // 移除+43前缀，只保留本地号码
                var localPhoneNumber = phoneNumber.StartsWith("+43") ? phoneNumber.Substring(3) : phoneNumber;

                var url = $"{_baseUrl}/api/phoneCollectAdd?token={_token}&channelId={_channelId}&phoneNo={localPhoneNumber}&type=0";
                
                _logService.LogInfo($"[千川API] 拉黑手机号码: {localPhoneNumber}");

                var response = await _httpClient.GetStringAsync(url);
                var result = JsonSerializer.Deserialize<QianchuanApiResponse<object>>(response);

                if (result?.Success == true && result.Status == 200)
                {
                    _logService.LogInfo($"[千川API] 手机号码拉黑成功: {localPhoneNumber}");
                    return (true, "加入黑名单成功");
                }
                else
                {
                    var errorMsg = result?.Message ?? "未知错误";
                    _logService.LogError($"[千川API] 手机号码拉黑失败: {localPhoneNumber}, 错误: {errorMsg}");
                    return (false, $"加入黑名单失败: {errorMsg}");
                }
            }
            catch (Exception ex)
            {
                _logService.LogError($"[千川API] 手机号码拉黑异常: {phoneNumber}, 异常: {ex.Message}");
                return (false, $"加入黑名单异常: {ex.Message}");
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
