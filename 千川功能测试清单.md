# 千川功能测试清单

## 测试前准备

### 1. 配置检查
- [ ] 确认 `config.json` 中已添加千川配置：
  ```json
  "QianchuanToken": "170154-*-c774223f-7655-4425-9871-c8bc459d4a6b",
  "QianchuanChannelId": "7",
  "QianchuanBaseUrl": "https://api.qc86.shop"
  ```

### 2. 界面检查
- [ ] 启动程序，检查手机号码模式选项顺序：
  - [ ] 榴莲（第一个）
  - [ ] 千川（第二个）
  - [ ] 美国（第三个）
  - [ ] 手动模式（第四个）

## 单线程模式测试

### 1. 基本功能测试
- [ ] 选择千川模式
- [ ] 保存配置（应显示千川模式提示）
- [ ] 检查状态指示器显示为紫色
- [ ] 检查模式文本显示为"千川"

### 2. 注册流程测试
- [ ] 启动单线程注册
- [ ] 检查第四页是否自动选择奥地利（+43）区号
- [ ] 检查第六页是否自动选择奥地利（+43）区号
- [ ] 观察手机号码获取日志
- [ ] 观察验证码获取日志

### 3. API调用测试
- [ ] 检查获取手机号码API调用
- [ ] 检查验证码提取（从modle字段提取4位数字）
- [ ] 检查验证码转换为小写
- [ ] 检查重试机制（如果API失败）

## 多线程模式测试

### 1. 多线程启动测试
- [ ] 设置线程数量（建议2-3个）
- [ ] 选择千川模式
- [ ] 启动多线程注册
- [ ] 检查是否为每个线程分配了独立的手机号码

### 2. 号码分配测试
- [ ] 观察逐个获取手机号码的过程
- [ ] 检查每个线程是否获得了+43开头的号码
- [ ] 验证线程间号码不重复
- [ ] 测试部分线程获取失败的情况

### 3. 验证码管理测试
- [ ] 检查多线程验证码获取
- [ ] 验证千川验证码提取逻辑
- [ ] 检查黑名单管理器工作状态

## 错误处理测试

### 1. API失败测试
- [ ] 模拟网络错误
- [ ] 检查重试机制（3次重试）
- [ ] 验证最终失败后转为手动模式

### 2. 配置错误测试
- [ ] 测试空token配置
- [ ] 测试错误的channelId
- [ ] 检查错误提示信息

### 3. 多线程失败测试
- [ ] 测试部分线程获取号码失败
- [ ] 验证失败线程转为手动模式
- [ ] 检查成功线程继续自动执行

## 黑名单功能测试

### 1. 自动黑名单测试
- [ ] 完成一次注册流程
- [ ] 检查使用过的号码是否加入黑名单队列
- [ ] 观察定时批量拉黑（30秒间隔）

### 2. 手动黑名单测试
- [ ] 检查立即拉黑功能
- [ ] 验证拉黑API调用
- [ ] 检查拉黑结果统计

### 3. 程序退出清理测试
- [ ] 在有待拉黑号码时关闭程序
- [ ] 检查退出时是否自动清理黑名单队列
- [ ] 验证清理日志记录

## 兼容性测试

### 1. 与其他模式兼容性
- [ ] 从千川模式切换到榴莲模式
- [ ] 从千川模式切换到美国模式
- [ ] 从千川模式切换到手动模式
- [ ] 检查切换后配置是否正确

### 2. 浏览器模式兼容性
- [ ] 无痕Chrome + 千川模式
- [ ] 默认Chrome + 千川模式
- [ ] 多线程 + 千川模式

## 日志和监控测试

### 1. 日志记录测试
- [ ] 检查千川API调用日志
- [ ] 验证错误日志记录
- [ ] 检查成功操作日志

### 2. 状态监控测试
- [ ] 观察状态消息更新
- [ ] 检查进度指示
- [ ] 验证错误提示

## 性能测试

### 1. 单线程性能
- [ ] 测试单次号码获取时间
- [ ] 测试验证码获取时间
- [ ] 检查内存使用情况

### 2. 多线程性能
- [ ] 测试多线程号码分配时间
- [ ] 检查并发API调用性能
- [ ] 验证资源使用情况

## 边界条件测试

### 1. 网络条件测试
- [ ] 慢网络环境测试
- [ ] 网络中断恢复测试
- [ ] 超时处理测试

### 2. 数据边界测试
- [ ] 大量线程测试（10+线程）
- [ ] 长时间运行测试
- [ ] 内存泄漏检查

## 测试结果记录

### 通过的测试项
- [ ] 记录通过的功能点
- [ ] 记录性能数据
- [ ] 记录稳定性表现

### 发现的问题
- [ ] 记录bug和问题
- [ ] 记录改进建议
- [ ] 记录性能瓶颈

### 测试总结
- [ ] 功能完整性评估
- [ ] 稳定性评估
- [ ] 性能评估
- [ ] 用户体验评估

## 注意事项

1. **API限制**: 千川API可能有频率限制，注意观察
2. **网络环境**: 确保网络连接稳定
3. **配置正确**: 确认token和channelId有效
4. **日志监控**: 密切关注日志输出，及时发现问题
5. **资源清理**: 测试完成后检查资源是否正确释放
