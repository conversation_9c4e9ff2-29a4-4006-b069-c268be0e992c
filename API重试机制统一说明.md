# API重试机制统一说明

## 概述
为了便于统一管理榴莲和千川API的重试逻辑，创建了`ApiRetryService`统一重试服务。现在两个API服务都使用相同的重试机制，便于后续维护和修改。

## 重试配置

### 当前配置
```csharp
public static class ApiRetryService
{
    // 统一的重试配置
    public const int MaxRetries = 3;        // 最大重试次数：3次
    public const int RetryDelayMs = 1000;   // 重试间隔：1000毫秒（1秒）
}
```

### 修改重试配置
如果需要修改重试次数或间隔，只需要在`Services/ApiRetryService.cs`文件中修改这两个常量：

1. **修改重试次数**：
   ```csharp
   public const int MaxRetries = 5;  // 改为5次重试
   ```

2. **修改重试间隔**：
   ```csharp
   public const int RetryDelayMs = 2000;  // 改为2秒间隔
   ```

## 使用方式

### 榴莲API使用示例
```csharp
public async Task<(bool Success, PhoneNumberInfo? PhoneInfo, string Message)> GetPhoneNumberAsync()
{
    return await ApiRetryService.ExecuteWithRetryTupleAsync(
        async () => await GetPhoneNumberInternalAsync(),
        "获取手机号码",
        "榴莲API"
    );
}
```

### 千川API使用示例
```csharp
public async Task<(bool Success, QianchuanPhoneInfo? PhoneInfo, string Message)> GetPhoneNumberAsync()
{
    return await ApiRetryService.ExecuteWithRetryTupleAsync(
        async () => await GetPhoneNumberInternalAsync(),
        "获取手机号码",
        "千川API"
    );
}
```

## 重试逻辑

### 重试触发条件
以下情况会触发重试：
1. **HttpRequestException** 且消息包含 "HttpClient.Timeout"
2. **TaskCanceledException** 且是超时异常
3. **其他Exception** 类型的异常

### 重试流程
1. 执行API调用
2. 如果成功，直接返回结果
3. 如果失败且是网络异常，等待指定间隔后重试
4. 重复步骤1-3，直到达到最大重试次数
5. 如果所有重试都失败，返回失败结果

### 日志记录
重试过程中会记录详细日志：
- 每次尝试的开始
- 异常信息和重试计划
- 最终结果

## 代码结构

### 文件位置
- **重试服务**: `Services/ApiRetryService.cs`
- **榴莲API**: `Services/PhoneApiService.cs`
- **千川API**: `Services/QianchuanPhoneApiService.cs`

### 方法结构
每个API服务都分为两个方法：
1. **公共方法**: 调用重试服务
2. **内部方法**: 实际的API调用逻辑（不包含重试）

例如：
```csharp
// 公共方法 - 包含重试逻辑
public async Task<(bool Success, T?, string Message)> GetDataAsync()
{
    return await ApiRetryService.ExecuteWithRetryTupleAsync(
        async () => await GetDataInternalAsync(),
        "操作名称",
        "服务前缀"
    );
}

// 内部方法 - 纯API调用
private async Task<(bool Success, T?, string Message)> GetDataInternalAsync()
{
    // 实际的API调用逻辑
}
```

## 优势

### 1. 统一管理
- 所有API的重试逻辑集中在一个地方
- 修改重试配置只需要改一个文件
- 保证所有API的重试行为一致

### 2. 易于维护
- 重试逻辑与业务逻辑分离
- 代码复用，减少重复代码
- 便于单元测试

### 3. 灵活配置
- 可以轻松调整重试次数和间隔
- 可以为不同操作设置不同的重试策略
- 支持不同的返回类型

## 扩展说明

### 添加新的API服务
如果需要添加新的API服务，只需要：
1. 创建API服务类
2. 使用`ApiRetryService.ExecuteWithRetryTupleAsync`包装API调用
3. 实现内部的API调用方法

### 自定义重试策略
如果需要为特定API设置不同的重试策略，可以：
1. 在`ApiRetryService`中添加新的方法
2. 传入自定义的重试参数
3. 或者创建专门的重试方法

## 测试建议

### 1. 正常情况测试
- 验证API调用成功时不会重试
- 验证返回结果正确

### 2. 异常情况测试
- 模拟网络超时，验证重试机制
- 模拟API错误，验证错误处理
- 验证重试次数和间隔正确

### 3. 配置测试
- 修改重试配置，验证生效
- 测试不同重试次数的效果

## 注意事项

1. **重试间隔**: 当前设置为1秒，避免过于频繁的API调用
2. **重试次数**: 当前设置为3次，平衡成功率和响应时间
3. **异常处理**: 只对网络异常进行重试，业务逻辑错误不重试
4. **日志记录**: 重试过程会产生较多日志，注意日志文件大小
5. **性能影响**: 重试会增加总的响应时间，需要权衡
